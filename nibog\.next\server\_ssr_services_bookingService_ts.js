"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_services_bookingService_ts";
exports.ids = ["_ssr_services_bookingService_ts"];
exports.modules = {

/***/ "(ssr)/./services/bookingService.ts":
/*!************************************!*\
  !*** ./services/bookingService.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertBookingRefFormat: () => (/* binding */ convertBookingRefFormat),\n/* harmony export */   createBooking: () => (/* binding */ createBooking),\n/* harmony export */   findMostLikelySlotForBooking: () => (/* binding */ findMostLikelySlotForBooking),\n/* harmony export */   getAllBookings: () => (/* binding */ getAllBookings),\n/* harmony export */   getBookingById: () => (/* binding */ getBookingById),\n/* harmony export */   getBookingPaymentDetails: () => (/* binding */ getBookingPaymentDetails),\n/* harmony export */   getEventGameSlotDetails: () => (/* binding */ getEventGameSlotDetails),\n/* harmony export */   getEventGameSlotDetailsBySlotId: () => (/* binding */ getEventGameSlotDetailsBySlotId),\n/* harmony export */   getEventWithVenueDetails: () => (/* binding */ getEventWithVenueDetails),\n/* harmony export */   getPaginatedBookings: () => (/* binding */ getPaginatedBookings),\n/* harmony export */   getTicketDetails: () => (/* binding */ getTicketDetails),\n/* harmony export */   updateBookingPaymentStatus: () => (/* binding */ updateBookingPaymentStatus),\n/* harmony export */   updateBookingStatus: () => (/* binding */ updateBookingStatus)\n/* harmony export */ });\n// Booking service for handling booking-related API calls\n/**\r\n * Get all bookings with error handling and timeout\r\n * @returns Promise with array of bookings\r\n */ async function getAllBookings() {\n    try {\n        // Use our internal API route to avoid CORS issues\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000); // 30 second timeout\n        try {\n            const response = await fetch('/api/bookings/get-all', {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                throw new Error(`API returned error status: ${response.status}`);\n            }\n            const data = await response.json();\n            // Handle both old format (array) and new paginated format\n            if (Array.isArray(data)) {\n                return data;\n            } else if (data.data && Array.isArray(data.data)) {\n                return data.data;\n            }\n            return [];\n        } catch (fetchError) {\n            clearTimeout(timeoutId);\n            if (fetchError.name === 'AbortError') {\n                throw new Error(\"Request timed out. The server took too long to respond.\");\n            }\n            throw fetchError;\n        }\n    } catch (error) {\n        throw error;\n    }\n}\n/**\r\n * Get paginated bookings with error handling and timeout\r\n * @param params Pagination parameters\r\n * @returns Promise with paginated bookings response\r\n */ async function getPaginatedBookings(params = {}) {\n    try {\n        const { page = 1, limit = 100 } = params;\n        // Use our internal API route to avoid CORS issues\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000); // 30 second timeout\n        try {\n            const url = new URL('/api/bookings/get-all', window.location.origin);\n            url.searchParams.set('page', page.toString());\n            url.searchParams.set('limit', limit.toString());\n            const response = await fetch(url.toString(), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                throw new Error(`API returned error status: ${response.status}`);\n            }\n            const data = await response.json();\n            // Ensure we have the expected paginated format\n            if (data.data && data.pagination) {\n                return data;\n            }\n            // Fallback for old format\n            if (Array.isArray(data)) {\n                return {\n                    data,\n                    pagination: {\n                        page: 1,\n                        limit: data.length,\n                        total: data.length,\n                        totalPages: 1,\n                        hasNext: false,\n                        hasPrev: false\n                    }\n                };\n            }\n            throw new Error(\"Invalid response format\");\n        } catch (fetchError) {\n            clearTimeout(timeoutId);\n            if (fetchError.name === 'AbortError') {\n                throw new Error(\"Request timed out. The server took too long to respond.\");\n            }\n            throw fetchError;\n        }\n    } catch (error) {\n        throw error;\n    }\n}\n/**\r\n * Update booking status\r\n * @param bookingId Booking ID\r\n * @param status New status\r\n * @returns Promise with updated booking data\r\n */ async function updateBookingStatus(bookingId, status) {\n    try {\n        // Use our internal API route to avoid CORS issues\n        const response = await fetch('/api/bookings/update-status', {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                bookingId,\n                status\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            let errorMessage = `API returned error status: ${response.status}`;\n            try {\n                const errorData = JSON.parse(errorText);\n                if (errorData.error) {\n                    errorMessage = errorData.error;\n                }\n            } catch (e) {\n            // If we can't parse the error as JSON, use the status code\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        // The API returns an array with a single booking object\n        if (Array.isArray(data) && data.length > 0) {\n            return data[0];\n        } else if (!Array.isArray(data)) {\n            return data;\n        }\n        throw new Error(\"Invalid response format from status update API\");\n    } catch (error) {\n        throw error;\n    }\n}\n/**\r\n * Get booking by ID\r\n * @param bookingId Booking ID\r\n * @returns Promise with booking data\r\n */ async function getBookingById(bookingId) {\n    try {\n        // Create an AbortController for timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 15000); // 15 second timeout\n        // Use our internal API route to avoid CORS issues\n        const response = await fetch(`/api/bookings/get/${bookingId}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            // If it's a 404, the booking doesn't exist\n            if (response.status === 404) {\n                throw new Error(`Booking with ID ${bookingId} not found`);\n            }\n            // For other errors, throw immediately without fallback to prevent loops\n            throw new Error(`Failed to fetch booking: ${response.status}`);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        // Handle timeout errors\n        if (error.name === 'AbortError') {\n            throw new Error('Request timeout - the booking service is taking too long to respond');\n        }\n        // Re-throw the error without fallback to prevent infinite loops\n        throw error;\n    }\n}\n/**\r\n * Get event details with venue information by event ID\r\n * @param eventId Event ID\r\n * @returns Promise with event details including venue information\r\n */ async function getEventWithVenueDetails(eventId) {\n    try {\n        console.log('Fetching event details with venue information for event ID:', eventId);\n        const response = await fetch(`/api/events/get-with-games`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                eventId\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`Failed to fetch event details: ${response.status}`);\n        }\n        const eventData = await response.json();\n        console.log('Event details with venue information:', eventData);\n        return eventData;\n    } catch (error) {\n        console.error('Error fetching event details with venue information:', error);\n        throw error;\n    }\n}\n/**\r\n * Converts booking references between formats\r\n * @param ref The booking reference to convert\r\n * @param targetFormat The target format for the conversion ('B' or 'PPT')\r\n * @returns The converted booking reference\r\n */ function convertBookingRefFormat(ref, targetFormat = 'PPT') {\n    if (!ref) return '';\n    // Remove quotes if it's a JSON string\n    let cleanRef = ref;\n    if (cleanRef.startsWith('\"') && cleanRef.endsWith('\"')) {\n        cleanRef = cleanRef.slice(1, -1);\n    }\n    // Extract numeric parts based on current format\n    let numericPart = '';\n    const currentDate = new Date();\n    const year = currentDate.getFullYear().toString().slice(-2);\n    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');\n    const day = currentDate.getDate().toString().padStart(2, '0');\n    if (cleanRef.startsWith('B')) {\n        // Extract from B format (B0000123)\n        numericPart = cleanRef.replace(/^B(\\d+)$/, '$1');\n        if (targetFormat === 'B') {\n            // Already in B format, just normalize\n            return `B${numericPart.padStart(7, '0')}`;\n        } else {\n            // Convert B -> PPT format\n            // Use current date + numeric part as the identifier\n            return `PPT${year}${month}${day}${numericPart.slice(-3).padStart(3, '0')}`;\n        }\n    } else if (cleanRef.startsWith('PPT')) {\n        // Extract from PPT format (PPTYYMMDDxxx)\n        // PPT format typically has date embedded in it\n        console.log(`Converting PPT reference: ${cleanRef}`);\n        console.log(`PPT reference length: ${cleanRef.length}`);\n        console.log(`PPT reference characters:`, cleanRef.split('').map((c)=>`${c}(${c.charCodeAt(0)})`));\n        // More flexible regex that handles various PPT formats\n        const pptMatch = cleanRef.match(/^PPT(\\d{6})(\\d+)$/);\n        console.log(`PPT regex match result:`, pptMatch);\n        if (pptMatch) {\n            const dateStr = pptMatch[1]; // YYMMDD part\n            const idPart = pptMatch[2]; // xxx part (numeric ID)\n            console.log(`PPT matched - dateStr: ${dateStr}, idPart: ${idPart}`);\n            if (targetFormat === 'PPT') {\n                // Already in PPT format, return as-is to preserve original date\n                console.log(`Returning PPT as-is: ${cleanRef}`);\n                return cleanRef;\n            } else {\n                // Convert PPT -> B format\n                // Use the numeric part as is, pad to 7 digits\n                return `B${idPart.padStart(7, '0')}`;\n            }\n        } else {\n            // Try alternative patterns for PPT references\n            console.log(`Primary PPT regex failed, trying alternative patterns...`);\n            // Check if it's a valid PPT format with any number of digits after the date\n            const altMatch = cleanRef.match(/^PPT(\\d+)$/);\n            if (altMatch && altMatch[1].length >= 9) {\n                const fullNumber = altMatch[1];\n                const dateStr = fullNumber.substring(0, 6); // First 6 digits as date\n                const idPart = fullNumber.substring(6); // Rest as ID\n                console.log(`Alternative PPT pattern matched - dateStr: ${dateStr}, idPart: ${idPart}`);\n                if (targetFormat === 'PPT') {\n                    // Already in PPT format, return as-is to preserve original date\n                    console.log(`Returning PPT as-is: ${cleanRef}`);\n                    return cleanRef;\n                } else {\n                    // Convert PPT -> B format\n                    return `B${idPart.padStart(7, '0')}`;\n                }\n            }\n            // Last resort - if target format is PPT and input is already PPT, preserve it\n            if (targetFormat === 'PPT') {\n                console.log(`PPT reference considered malformed but preserving original: ${cleanRef}`);\n                return cleanRef; // Preserve original PPT reference to avoid date changes\n            } else {\n                // Only convert to B format if explicitly requested\n                console.log(`PPT reference considered malformed, converting to B format`);\n                numericPart = cleanRef.replace(/\\D/g, '');\n                const fallbackResult = `B${numericPart.slice(-7).padStart(7, '0')}`;\n                console.log(`Fallback result: ${fallbackResult}`);\n                return fallbackResult;\n            }\n        }\n    } else {\n        // Unknown format, extract any numeric parts\n        numericPart = cleanRef.replace(/\\D/g, '');\n        return targetFormat === 'B' ? `B${numericPart.slice(-7).padStart(7, '0')}` : `PPT${year}${month}${day}${numericPart.slice(-3).padStart(3, '0')}`;\n    }\n}\n// New function to fetch detailed ticket information using booking reference\nasync function getTicketDetails(bookingRef) {\n    try {\n        console.log('Fetching ticket details with booking reference:', bookingRef);\n        // API expects booking_ref_id in PPT format for this specific endpoint\n        // If already in PPT format, use as-is. Otherwise convert from B format.\n        let formattedRef = bookingRef;\n        if (!bookingRef.startsWith('PPT')) {\n            formattedRef = convertBookingRefFormat(bookingRef, 'PPT');\n            console.log(`Converted booking reference to API format: ${bookingRef} -> ${formattedRef}`);\n        } else {\n            console.log(`Booking reference already in PPT format: ${bookingRef}`);\n        }\n        // Strip any JSON formatting if it was stored as JSON string\n        if (formattedRef.startsWith('\"') && formattedRef.endsWith('\"')) {\n            formattedRef = formattedRef.slice(1, -1);\n            console.log('Stripped JSON quotes from booking reference:', formattedRef);\n        }\n        console.log('Making API call with formatted booking reference:', formattedRef);\n        const response = await fetch('https://ai.alviongs.com/webhook/v1/nibog/tickect/booking_ref/details', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                booking_ref_id: formattedRef // Using exact parameter name expected by the API\n            })\n        });\n        if (!response.ok) {\n            console.error(`API returned error status: ${response.status} ${response.statusText}`);\n            const errorText = await response.text();\n            console.error('Error response body:', errorText);\n            throw new Error(`Failed to fetch ticket details: ${response.status}`);\n        }\n        const data = await response.json();\n        console.log('Received ticket details:', data);\n        return data;\n    } catch (error) {\n        console.error('Error fetching ticket details:', error);\n        throw error;\n    }\n}\n/**\r\n * Get event game slot details by slot ID (preferred method)\r\n * @param slotId Slot ID\r\n * @returns Promise with slot details or null\r\n */ async function getEventGameSlotDetailsBySlotId(slotId) {\n    try {\n        const response = await fetch('https://ai.alviongs.com/webhook/v1/nibog/event-game-slot/get', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                id: slotId\n            })\n        });\n        if (response.ok) {\n            const slotDataArray = await response.json();\n            // The API returns an array, get the first item\n            if (slotDataArray && Array.isArray(slotDataArray) && slotDataArray.length > 0) {\n                const slotData = slotDataArray[0];\n                return {\n                    slot_id: slotData.id,\n                    custom_title: slotData.custom_title,\n                    custom_description: slotData.custom_description,\n                    custom_price: slotData.custom_price,\n                    slot_price: slotData.slot_price,\n                    start_time: slotData.start_time,\n                    end_time: slotData.end_time,\n                    max_participants: slotData.max_participants\n                };\n            }\n        }\n    } catch (error) {\n        console.error('Error fetching slot details by slot ID:', error);\n    }\n    return null;\n}\n/**\r\n * Find the most likely slot for a booking based on event and game details\r\n * This is a workaround for when booking_games data is not available\r\n * @param booking Booking data\r\n * @returns Promise with slot details or null\r\n */ async function findMostLikelySlotForBooking(booking) {\n    try {\n        console.log('🔍 Finding most likely slot for booking:', booking.booking_id);\n        console.log('📋 Booking details:', {\n            event_title: booking.event_title,\n            event_date: booking.event_event_date,\n            game_name: booking.game_name,\n            total_amount: booking.total_amount,\n            booking_created_at: booking.booking_created_at\n        });\n        // Get all slots first with timeout protection\n        const slotsController = new AbortController();\n        const slotsTimeout = setTimeout(()=>slotsController.abort(), 10000); // 10 second timeout\n        const slotsResponse = await fetch('https://ai.alviongs.com/webhook/v1/nibog/event-game-slot/get-all', {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            signal: slotsController.signal\n        });\n        clearTimeout(slotsTimeout);\n        if (!slotsResponse.ok) {\n            console.error('❌ Failed to fetch slots:', slotsResponse.status);\n            return null;\n        }\n        const allSlots = await slotsResponse.json();\n        console.log(`📊 Total slots available: ${allSlots.length}`);\n        // Get all events to find the matching event ID with timeout protection\n        const eventsController = new AbortController();\n        const eventsTimeout = setTimeout(()=>eventsController.abort(), 10000); // 10 second timeout\n        const eventsResponse = await fetch('https://ai.alviongs.com/webhook/v1/nibog/event/get-all', {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            signal: eventsController.signal\n        });\n        clearTimeout(eventsTimeout);\n        if (!eventsResponse.ok) {\n            console.error('❌ Failed to fetch events:', eventsResponse.status);\n            return null;\n        }\n        const allEvents = await eventsResponse.json();\n        // Find the event that matches the booking\n        const matchingEvent = allEvents.find((event)=>{\n            const eventDate = new Date(event.event_date).toDateString();\n            const bookingDate = new Date(booking.event_event_date).toDateString();\n            return event.title === booking.event_title && eventDate === bookingDate;\n        });\n        if (!matchingEvent) {\n            console.log('❌ No matching event found for:', booking.event_title);\n            return null;\n        }\n        console.log('✅ Found matching event:', matchingEvent.id, matchingEvent.title);\n        // Get all games to find the matching game ID with timeout protection\n        const gamesController = new AbortController();\n        const gamesTimeout = setTimeout(()=>gamesController.abort(), 10000); // 10 second timeout\n        const gamesResponse = await fetch('https://ai.alviongs.com/webhook/v1/nibog/baby-games/get-all', {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            signal: gamesController.signal\n        });\n        clearTimeout(gamesTimeout);\n        if (!gamesResponse.ok) {\n            console.error('❌ Failed to fetch games:', gamesResponse.status);\n            return null;\n        }\n        const allGames = await gamesResponse.json();\n        // Find the game that matches the booking\n        const matchingGame = allGames.find((game)=>game.game_title === booking.game_name || game.name === booking.game_name || game.title === booking.game_name);\n        if (!matchingGame) {\n            console.log('❌ No matching game found for:', booking.game_name);\n            return null;\n        }\n        console.log('✅ Found matching game:', matchingGame.id, matchingGame.game_title || matchingGame.name);\n        // Find slots that match both event and game\n        const matchingSlots = allSlots.filter((slot)=>slot.event_id === matchingEvent.id && slot.game_id === matchingGame.id);\n        console.log(`🎯 Found ${matchingSlots.length} matching slots for event ${matchingEvent.id} + game ${matchingGame.id}`);\n        if (matchingSlots.length === 0) {\n            console.log('❌ No matching slots found');\n            return null;\n        }\n        // Log all matching slots for debugging\n        matchingSlots.forEach((slot, index)=>{\n            console.log(`  ${index + 1}. Slot ${slot.id}: \"${slot.custom_title || 'No custom title'}\" - Price: ${slot.slot_price}`);\n        });\n        // If there's only one slot, use it\n        if (matchingSlots.length === 1) {\n            const slot = matchingSlots[0];\n            console.log('✅ Using single matching slot:', slot.id, slot.custom_title);\n            return await getEventGameSlotDetailsBySlotId(slot.id);\n        }\n        // IMPROVED MATCHING LOGIC for multiple slots:\n        // 1. Try to match by price (booking total_amount should match slot_price + any addons)\n        const bookingAmount = parseFloat(booking.total_amount);\n        const priceMatchingSlots = matchingSlots.filter((slot)=>{\n            const slotPrice = parseFloat(slot.slot_price || slot.custom_price || '0');\n            // Allow for small differences due to addons, taxes, etc.\n            return Math.abs(bookingAmount - slotPrice) <= 2; // Within $2 difference\n        });\n        if (priceMatchingSlots.length === 1) {\n            const slot = priceMatchingSlots[0];\n            console.log('✅ Using price-matched slot:', slot.id, slot.custom_title, `(${slot.slot_price})`);\n            return await getEventGameSlotDetailsBySlotId(slot.id);\n        }\n        // 2. Try to match by custom title (slots with custom titles are more likely to be the booked ones)\n        const customSlots = matchingSlots.filter((slot)=>slot.custom_title && slot.custom_title.trim() !== '' && slot.custom_title !== booking.game_name);\n        if (customSlots.length === 1) {\n            const slot = customSlots[0];\n            console.log('✅ Using custom-titled slot:', slot.id, slot.custom_title);\n            return await getEventGameSlotDetailsBySlotId(slot.id);\n        }\n        // 3. Try to match by creation time (slots created around the same time as booking)\n        if (booking.booking_created_at) {\n            const bookingTime = new Date(booking.booking_created_at);\n            const timeMatchingSlots = matchingSlots.filter((slot)=>{\n                if (!slot.created_at) return false;\n                const slotTime = new Date(slot.created_at);\n                const timeDiff = Math.abs(bookingTime.getTime() - slotTime.getTime());\n                // Within 24 hours\n                return timeDiff <= 24 * 60 * 60 * 1000;\n            });\n            if (timeMatchingSlots.length === 1) {\n                const slot = timeMatchingSlots[0];\n                console.log('✅ Using time-matched slot:', slot.id, slot.custom_title);\n                return await getEventGameSlotDetailsBySlotId(slot.id);\n            }\n        }\n        // 4. Fallback: Use the slot with the most recent creation time\n        const sortedSlots = matchingSlots.sort((a, b)=>{\n            const timeA = new Date(a.created_at || 0).getTime();\n            const timeB = new Date(b.created_at || 0).getTime();\n            return timeB - timeA; // Most recent first\n        });\n        const slot = sortedSlots[0];\n        console.log('✅ Using most recent slot as fallback:', slot.id, slot.custom_title);\n        return await getEventGameSlotDetailsBySlotId(slot.id);\n    } catch (error) {\n        console.error('❌ Error finding most likely slot:', error);\n        return null;\n    }\n}\n/**\r\n * Get event game slot details by event and game IDs (fallback method)\r\n * @param eventId Event ID\r\n * @param gameId Game ID\r\n * @returns Promise with slot details or null\r\n */ async function getEventGameSlotDetails(eventId, gameId) {\n    try {\n        const response = await fetch('https://ai.alviongs.com/webhook/v1/nibog/event-game-slot/get-all', {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        if (response.ok) {\n            const allSlots = await response.json();\n            const matchingSlot = allSlots.find((slot)=>slot.event_id === eventId && slot.game_id === gameId);\n            if (matchingSlot) {\n                return {\n                    slot_id: matchingSlot.id,\n                    custom_title: matchingSlot.custom_title,\n                    custom_description: matchingSlot.custom_description,\n                    custom_price: matchingSlot.custom_price,\n                    slot_price: matchingSlot.slot_price,\n                    start_time: matchingSlot.start_time,\n                    end_time: matchingSlot.end_time,\n                    max_participants: matchingSlot.max_participants\n                };\n            }\n        }\n    } catch (error) {\n        console.error('Error fetching slot details:', error);\n    }\n    return null;\n}\n/**\r\n * Get payment details for a booking\r\n * @param bookingId Booking ID\r\n * @returns Promise with payment details or null\r\n */ async function getBookingPaymentDetails(bookingId) {\n    try {\n        const response = await fetch('https://ai.alviongs.com/webhook/v1/nibog/payments/get-all', {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        if (response.ok) {\n            const allPayments = await response.json();\n            const payment = allPayments.find((p)=>p.booking_id === bookingId);\n            if (payment) {\n                return {\n                    payment_id: payment.payment_id,\n                    actual_payment_status: payment.payment_status,\n                    transaction_id: payment.transaction_id,\n                    payment_date: payment.payment_date,\n                    payment_method: payment.payment_method\n                };\n            }\n        }\n    } catch (error) {\n        console.error('Error fetching payment details:', error);\n    }\n    return null;\n}\n/**\r\n * Update booking payment status\r\n * @param bookingId Booking ID\r\n * @param paymentStatus New payment status\r\n * @returns Promise with updated booking data\r\n */ async function updateBookingPaymentStatus(bookingId, paymentStatus) {\n    try {\n        console.log(`Updating booking ${bookingId} payment status to: ${paymentStatus}`);\n        // Use our internal API route that handles both payment status and booking status updates\n        const response = await fetch('/api/bookings/update-payment-status', {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                bookingId,\n                paymentStatus\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Failed to update booking payment status: ${response.status} - ${errorText}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error('Error updating booking payment status:', error);\n        throw error;\n    }\n}\n/**\r\n * Create a new booking\r\n * @param bookingData The booking data to create\r\n * @returns Promise with the created booking data\r\n */ async function createBooking(bookingData) {\n    try {\n        // Use our internal API route to avoid CORS issues\n        const response = await fetch('/api/bookings/create', {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(bookingData)\n        });\n        if (!response.ok) {\n            throw new Error(`API returned error status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zZXJ2aWNlcy9ib29raW5nU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUEseURBQXlEO0FBeUZ6RDs7O0NBR0MsR0FDTSxlQUFlQTtJQUNwQixJQUFJO1FBQ0Ysa0RBQWtEO1FBQ2xELE1BQU1DLGFBQWEsSUFBSUM7UUFDdkIsTUFBTUMsWUFBWUMsV0FBVyxJQUFNSCxXQUFXSSxLQUFLLElBQUksUUFBUSxvQkFBb0I7UUFFbkYsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSx5QkFBeUI7Z0JBQ3BEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLFFBQVFULFdBQVdTLE1BQU07WUFDM0I7WUFFQUMsYUFBYVI7WUFFYixJQUFJLENBQUNHLFNBQVNNLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNLENBQUMsMkJBQTJCLEVBQUVQLFNBQVNRLE1BQU0sRUFBRTtZQUNqRTtZQUVBLE1BQU1DLE9BQU8sTUFBTVQsU0FBU1UsSUFBSTtZQUVoQywwREFBMEQ7WUFDMUQsSUFBSUMsTUFBTUMsT0FBTyxDQUFDSCxPQUFPO2dCQUN2QixPQUFPQTtZQUNULE9BQU8sSUFBSUEsS0FBS0EsSUFBSSxJQUFJRSxNQUFNQyxPQUFPLENBQUNILEtBQUtBLElBQUksR0FBRztnQkFDaEQsT0FBT0EsS0FBS0EsSUFBSTtZQUNsQjtZQUVBLE9BQU8sRUFBRTtRQUNYLEVBQUUsT0FBT0ksWUFBaUI7WUFDeEJSLGFBQWFSO1lBQ2IsSUFBSWdCLFdBQVdDLElBQUksS0FBSyxjQUFjO2dCQUNwQyxNQUFNLElBQUlQLE1BQU07WUFDbEI7WUFDQSxNQUFNTTtRQUNSO0lBQ0YsRUFBRSxPQUFPRSxPQUFZO1FBQ25CLE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7O0NBSUMsR0FDTSxlQUFlQyxxQkFBcUJDLFNBQTJCLENBQUMsQ0FBQztJQUN0RSxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxPQUFPLENBQUMsRUFBRUMsUUFBUSxHQUFHLEVBQUUsR0FBR0Y7UUFFbEMsa0RBQWtEO1FBQ2xELE1BQU10QixhQUFhLElBQUlDO1FBQ3ZCLE1BQU1DLFlBQVlDLFdBQVcsSUFBTUgsV0FBV0ksS0FBSyxJQUFJLFFBQVEsb0JBQW9CO1FBRW5GLElBQUk7WUFDRixNQUFNcUIsTUFBTSxJQUFJQyxJQUFJLHlCQUF5QkMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO1lBQ25FSixJQUFJSyxZQUFZLENBQUNDLEdBQUcsQ0FBQyxRQUFRUixLQUFLUyxRQUFRO1lBQzFDUCxJQUFJSyxZQUFZLENBQUNDLEdBQUcsQ0FBQyxTQUFTUCxNQUFNUSxRQUFRO1lBRTVDLE1BQU0zQixXQUFXLE1BQU1DLE1BQU1tQixJQUFJTyxRQUFRLElBQUk7Z0JBQzNDekIsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxRQUFRVCxXQUFXUyxNQUFNO1lBQzNCO1lBRUFDLGFBQWFSO1lBRWIsSUFBSSxDQUFDRyxTQUFTTSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTSxDQUFDLDJCQUEyQixFQUFFUCxTQUFTUSxNQUFNLEVBQUU7WUFDakU7WUFFQSxNQUFNQyxPQUFPLE1BQU1ULFNBQVNVLElBQUk7WUFFaEMsK0NBQStDO1lBQy9DLElBQUlELEtBQUtBLElBQUksSUFBSUEsS0FBS21CLFVBQVUsRUFBRTtnQkFDaEMsT0FBT25CO1lBQ1Q7WUFFQSwwQkFBMEI7WUFDMUIsSUFBSUUsTUFBTUMsT0FBTyxDQUFDSCxPQUFPO2dCQUN2QixPQUFPO29CQUNMQTtvQkFDQW1CLFlBQVk7d0JBQ1ZWLE1BQU07d0JBQ05DLE9BQU9WLEtBQUtvQixNQUFNO3dCQUNsQkMsT0FBT3JCLEtBQUtvQixNQUFNO3dCQUNsQkUsWUFBWTt3QkFDWkMsU0FBUzt3QkFDVEMsU0FBUztvQkFDWDtnQkFDRjtZQUNGO1lBRUEsTUFBTSxJQUFJMUIsTUFBTTtRQUNsQixFQUFFLE9BQU9NLFlBQWlCO1lBQ3hCUixhQUFhUjtZQUNiLElBQUlnQixXQUFXQyxJQUFJLEtBQUssY0FBYztnQkFDcEMsTUFBTSxJQUFJUCxNQUFNO1lBQ2xCO1lBQ0EsTUFBTU07UUFDUjtJQUNGLEVBQUUsT0FBT0UsT0FBWTtRQUNuQixNQUFNQTtJQUNSO0FBQ0Y7QUEyQkE7Ozs7O0NBS0MsR0FDTSxlQUFlbUIsb0JBQW9CQyxTQUFpQixFQUFFM0IsTUFBYztJQUN6RSxJQUFJO1FBQ0Ysa0RBQWtEO1FBQ2xELE1BQU1SLFdBQVcsTUFBTUMsTUFBTSwrQkFBK0I7WUFDMURDLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7WUFDQWlDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQkFDbkJIO2dCQUNBM0I7WUFDRjtRQUNGO1FBRUEsSUFBSSxDQUFDUixTQUFTTSxFQUFFLEVBQUU7WUFDaEIsTUFBTWlDLFlBQVksTUFBTXZDLFNBQVN3QyxJQUFJO1lBQ3JDLElBQUlDLGVBQWUsQ0FBQywyQkFBMkIsRUFBRXpDLFNBQVNRLE1BQU0sRUFBRTtZQUVsRSxJQUFJO2dCQUNGLE1BQU1rQyxZQUFZTCxLQUFLTSxLQUFLLENBQUNKO2dCQUM3QixJQUFJRyxVQUFVM0IsS0FBSyxFQUFFO29CQUNuQjBCLGVBQWVDLFVBQVUzQixLQUFLO2dCQUNoQztZQUNGLEVBQUUsT0FBTzZCLEdBQUc7WUFDViwyREFBMkQ7WUFDN0Q7WUFFQSxNQUFNLElBQUlyQyxNQUFNa0M7UUFDbEI7UUFFQSxNQUFNaEMsT0FBTyxNQUFNVCxTQUFTVSxJQUFJO1FBRWhDLHdEQUF3RDtRQUN4RCxJQUFJQyxNQUFNQyxPQUFPLENBQUNILFNBQVNBLEtBQUtvQixNQUFNLEdBQUcsR0FBRztZQUMxQyxPQUFPcEIsSUFBSSxDQUFDLEVBQUU7UUFDaEIsT0FBTyxJQUFJLENBQUNFLE1BQU1DLE9BQU8sQ0FBQ0gsT0FBTztZQUMvQixPQUFPQTtRQUNUO1FBRUEsTUFBTSxJQUFJRixNQUFNO0lBQ2xCLEVBQUUsT0FBT1EsT0FBWTtRQUNuQixNQUFNQTtJQUNSO0FBQ0Y7QUFFQTs7OztDQUlDLEdBQ00sZUFBZThCLGVBQWVWLFNBQTBCO0lBQzdELElBQUk7UUFDRix3Q0FBd0M7UUFDeEMsTUFBTXhDLGFBQWEsSUFBSUM7UUFDdkIsTUFBTUMsWUFBWUMsV0FBVyxJQUFNSCxXQUFXSSxLQUFLLElBQUksUUFBUSxvQkFBb0I7UUFFbkYsa0RBQWtEO1FBQ2xELE1BQU1DLFdBQVcsTUFBTUMsTUFBTSxDQUFDLGtCQUFrQixFQUFFa0MsV0FBVyxFQUFFO1lBQzdEakMsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBQyxRQUFRVCxXQUFXUyxNQUFNO1FBQzNCO1FBRUFDLGFBQWFSO1FBRWIsSUFBSSxDQUFDRyxTQUFTTSxFQUFFLEVBQUU7WUFDaEIsMkNBQTJDO1lBQzNDLElBQUlOLFNBQVNRLE1BQU0sS0FBSyxLQUFLO2dCQUMzQixNQUFNLElBQUlELE1BQU0sQ0FBQyxnQkFBZ0IsRUFBRTRCLFVBQVUsVUFBVSxDQUFDO1lBQzFEO1lBRUEsd0VBQXdFO1lBQ3hFLE1BQU0sSUFBSTVCLE1BQU0sQ0FBQyx5QkFBeUIsRUFBRVAsU0FBU1EsTUFBTSxFQUFFO1FBQy9EO1FBRUEsTUFBTUMsT0FBTyxNQUFNVCxTQUFTVSxJQUFJO1FBQ2hDLE9BQU9EO0lBQ1QsRUFBRSxPQUFPTSxPQUFZO1FBQ25CLHdCQUF3QjtRQUN4QixJQUFJQSxNQUFNRCxJQUFJLEtBQUssY0FBYztZQUMvQixNQUFNLElBQUlQLE1BQU07UUFDbEI7UUFFQSxnRUFBZ0U7UUFDaEUsTUFBTVE7SUFDUjtBQUNGO0FBZ0dBOzs7O0NBSUMsR0FDTSxlQUFlK0IseUJBQXlCQyxPQUFlO0lBQzVELElBQUk7UUFDRkMsUUFBUUMsR0FBRyxDQUFDLCtEQUErREY7UUFFM0UsTUFBTS9DLFdBQVcsTUFBTUMsTUFBTSxDQUFDLDBCQUEwQixDQUFDLEVBQUU7WUFDekRDLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7WUFDQWlDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQkFBRVM7WUFBUTtRQUNqQztRQUVBLElBQUksQ0FBQy9DLFNBQVNNLEVBQUUsRUFBRTtZQUNoQixNQUFNLElBQUlDLE1BQU0sQ0FBQywrQkFBK0IsRUFBRVAsU0FBU1EsTUFBTSxFQUFFO1FBQ3JFO1FBRUEsTUFBTTBDLFlBQVksTUFBTWxELFNBQVNVLElBQUk7UUFDckNzQyxRQUFRQyxHQUFHLENBQUMseUNBQXlDQztRQUVyRCxPQUFPQTtJQUNULEVBQUUsT0FBT25DLE9BQU87UUFDZGlDLFFBQVFqQyxLQUFLLENBQUMsd0RBQXdEQTtRQUN0RSxNQUFNQTtJQUNSO0FBQ0Y7QUFFQTs7Ozs7Q0FLQyxHQUNNLFNBQVNvQyx3QkFBd0JDLEdBQVcsRUFBRUMsZUFBNEIsS0FBSztJQUNwRixJQUFJLENBQUNELEtBQUssT0FBTztJQUVqQixzQ0FBc0M7SUFDdEMsSUFBSUUsV0FBV0Y7SUFDZixJQUFJRSxTQUFTQyxVQUFVLENBQUMsUUFBUUQsU0FBU0UsUUFBUSxDQUFDLE1BQU07UUFDdERGLFdBQVdBLFNBQVNHLEtBQUssQ0FBQyxHQUFHLENBQUM7SUFDaEM7SUFFQSxnREFBZ0Q7SUFDaEQsSUFBSUMsY0FBYztJQUNsQixNQUFNQyxjQUFjLElBQUlDO0lBQ3hCLE1BQU1DLE9BQU9GLFlBQVlHLFdBQVcsR0FBR25DLFFBQVEsR0FBRzhCLEtBQUssQ0FBQyxDQUFDO0lBQ3pELE1BQU1NLFFBQVEsQ0FBQ0osWUFBWUssUUFBUSxLQUFLLEdBQUdyQyxRQUFRLEdBQUdzQyxRQUFRLENBQUMsR0FBRztJQUNsRSxNQUFNQyxNQUFNUCxZQUFZUSxPQUFPLEdBQUd4QyxRQUFRLEdBQUdzQyxRQUFRLENBQUMsR0FBRztJQUV6RCxJQUFJWCxTQUFTQyxVQUFVLENBQUMsTUFBTTtRQUM1QixtQ0FBbUM7UUFDbkNHLGNBQWNKLFNBQVNjLE9BQU8sQ0FBQyxZQUFZO1FBRTNDLElBQUlmLGlCQUFpQixLQUFLO1lBQ3hCLHNDQUFzQztZQUN0QyxPQUFPLENBQUMsQ0FBQyxFQUFFSyxZQUFZTyxRQUFRLENBQUMsR0FBRyxNQUFNO1FBQzNDLE9BQU87WUFDTCwwQkFBMEI7WUFDMUIsb0RBQW9EO1lBQ3BELE9BQU8sQ0FBQyxHQUFHLEVBQUVKLE9BQU9FLFFBQVFHLE1BQU1SLFlBQVlELEtBQUssQ0FBQyxDQUFDLEdBQUdRLFFBQVEsQ0FBQyxHQUFHLE1BQU07UUFDNUU7SUFDRixPQUFPLElBQUlYLFNBQVNDLFVBQVUsQ0FBQyxRQUFRO1FBQ3JDLHlDQUF5QztRQUN6QywrQ0FBK0M7UUFDL0NQLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDBCQUEwQixFQUFFSyxVQUFVO1FBQ25ETixRQUFRQyxHQUFHLENBQUMsQ0FBQyxzQkFBc0IsRUFBRUssU0FBU3pCLE1BQU0sRUFBRTtRQUN0RG1CLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlCQUF5QixDQUFDLEVBQUVLLFNBQVNlLEtBQUssQ0FBQyxJQUFJQyxHQUFHLENBQUNDLENBQUFBLElBQUssR0FBR0EsRUFBRSxDQUFDLEVBQUVBLEVBQUVDLFVBQVUsQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUUvRix1REFBdUQ7UUFDdkQsTUFBTUMsV0FBV25CLFNBQVNvQixLQUFLLENBQUM7UUFDaEMxQixRQUFRQyxHQUFHLENBQUMsQ0FBQyx1QkFBdUIsQ0FBQyxFQUFFd0I7UUFFdkMsSUFBSUEsVUFBVTtZQUNaLE1BQU1FLFVBQVVGLFFBQVEsQ0FBQyxFQUFFLEVBQUUsY0FBYztZQUMzQyxNQUFNRyxTQUFTSCxRQUFRLENBQUMsRUFBRSxFQUFHLHdCQUF3QjtZQUNyRHpCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHVCQUF1QixFQUFFMEIsUUFBUSxVQUFVLEVBQUVDLFFBQVE7WUFFbEUsSUFBSXZCLGlCQUFpQixPQUFPO2dCQUMxQixnRUFBZ0U7Z0JBQ2hFTCxRQUFRQyxHQUFHLENBQUMsQ0FBQyxxQkFBcUIsRUFBRUssVUFBVTtnQkFDOUMsT0FBT0E7WUFDVCxPQUFPO2dCQUNMLDBCQUEwQjtnQkFDMUIsOENBQThDO2dCQUM5QyxPQUFPLENBQUMsQ0FBQyxFQUFFc0IsT0FBT1gsUUFBUSxDQUFDLEdBQUcsTUFBTTtZQUN0QztRQUNGLE9BQU87WUFDTCw4Q0FBOEM7WUFDOUNqQixRQUFRQyxHQUFHLENBQUMsQ0FBQyx3REFBd0QsQ0FBQztZQUV0RSw0RUFBNEU7WUFDNUUsTUFBTTRCLFdBQVd2QixTQUFTb0IsS0FBSyxDQUFDO1lBQ2hDLElBQUlHLFlBQVlBLFFBQVEsQ0FBQyxFQUFFLENBQUNoRCxNQUFNLElBQUksR0FBRztnQkFDdkMsTUFBTWlELGFBQWFELFFBQVEsQ0FBQyxFQUFFO2dCQUM5QixNQUFNRixVQUFVRyxXQUFXQyxTQUFTLENBQUMsR0FBRyxJQUFJLHlCQUF5QjtnQkFDckUsTUFBTUgsU0FBU0UsV0FBV0MsU0FBUyxDQUFDLElBQVEsYUFBYTtnQkFFekQvQixRQUFRQyxHQUFHLENBQUMsQ0FBQywyQ0FBMkMsRUFBRTBCLFFBQVEsVUFBVSxFQUFFQyxRQUFRO2dCQUV0RixJQUFJdkIsaUJBQWlCLE9BQU87b0JBQzFCLGdFQUFnRTtvQkFDaEVMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHFCQUFxQixFQUFFSyxVQUFVO29CQUM5QyxPQUFPQTtnQkFDVCxPQUFPO29CQUNMLDBCQUEwQjtvQkFDMUIsT0FBTyxDQUFDLENBQUMsRUFBRXNCLE9BQU9YLFFBQVEsQ0FBQyxHQUFHLE1BQU07Z0JBQ3RDO1lBQ0Y7WUFFQSw4RUFBOEU7WUFDOUUsSUFBSVosaUJBQWlCLE9BQU87Z0JBQzFCTCxRQUFRQyxHQUFHLENBQUMsQ0FBQyw0REFBNEQsRUFBRUssVUFBVTtnQkFDckYsT0FBT0EsVUFBVSx3REFBd0Q7WUFDM0UsT0FBTztnQkFDTCxtREFBbUQ7Z0JBQ25ETixRQUFRQyxHQUFHLENBQUMsQ0FBQywwREFBMEQsQ0FBQztnQkFDeEVTLGNBQWNKLFNBQVNjLE9BQU8sQ0FBQyxPQUFPO2dCQUN0QyxNQUFNWSxpQkFBaUIsQ0FBQyxDQUFDLEVBQUV0QixZQUFZRCxLQUFLLENBQUMsQ0FBQyxHQUFHUSxRQUFRLENBQUMsR0FBRyxNQUFNO2dCQUNuRWpCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGlCQUFpQixFQUFFK0IsZ0JBQWdCO2dCQUNoRCxPQUFPQTtZQUNUO1FBQ0Y7SUFDRixPQUFPO1FBQ0wsNENBQTRDO1FBQzVDdEIsY0FBY0osU0FBU2MsT0FBTyxDQUFDLE9BQU87UUFDdEMsT0FBT2YsaUJBQWlCLE1BQ3RCLENBQUMsQ0FBQyxFQUFFSyxZQUFZRCxLQUFLLENBQUMsQ0FBQyxHQUFHUSxRQUFRLENBQUMsR0FBRyxNQUFNLEdBQzVDLENBQUMsR0FBRyxFQUFFSixPQUFPRSxRQUFRRyxNQUFNUixZQUFZRCxLQUFLLENBQUMsQ0FBQyxHQUFHUSxRQUFRLENBQUMsR0FBRyxNQUFNO0lBQ3ZFO0FBQ0Y7QUFFQSw0RUFBNEU7QUFDckUsZUFBZWdCLGlCQUFpQkMsVUFBa0I7SUFDdkQsSUFBSTtRQUNGbEMsUUFBUUMsR0FBRyxDQUFDLG1EQUFtRGlDO1FBRS9ELHNFQUFzRTtRQUN0RSx3RUFBd0U7UUFDeEUsSUFBSUMsZUFBZUQ7UUFDbkIsSUFBSSxDQUFDQSxXQUFXM0IsVUFBVSxDQUFDLFFBQVE7WUFDakM0QixlQUFlaEMsd0JBQXdCK0IsWUFBWTtZQUNuRGxDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDJDQUEyQyxFQUFFaUMsV0FBVyxJQUFJLEVBQUVDLGNBQWM7UUFDM0YsT0FBTztZQUNMbkMsUUFBUUMsR0FBRyxDQUFDLENBQUMseUNBQXlDLEVBQUVpQyxZQUFZO1FBQ3RFO1FBRUEsNERBQTREO1FBQzVELElBQUlDLGFBQWE1QixVQUFVLENBQUMsUUFBUTRCLGFBQWEzQixRQUFRLENBQUMsTUFBTTtZQUM5RDJCLGVBQWVBLGFBQWExQixLQUFLLENBQUMsR0FBRyxDQUFDO1lBQ3RDVCxRQUFRQyxHQUFHLENBQUMsZ0RBQWdEa0M7UUFDOUQ7UUFFQW5DLFFBQVFDLEdBQUcsQ0FBQyxxREFBcURrQztRQUVqRSxNQUFNbkYsV0FBVyxNQUFNQyxNQUFNLHdFQUF3RTtZQUNuR0MsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBaUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUNuQjhDLGdCQUFnQkQsYUFBYyxpREFBaUQ7WUFDakY7UUFDRjtRQUVBLElBQUksQ0FBQ25GLFNBQVNNLEVBQUUsRUFBRTtZQUNoQjBDLFFBQVFqQyxLQUFLLENBQUMsQ0FBQywyQkFBMkIsRUFBRWYsU0FBU1EsTUFBTSxDQUFDLENBQUMsRUFBRVIsU0FBU3FGLFVBQVUsRUFBRTtZQUNwRixNQUFNOUMsWUFBWSxNQUFNdkMsU0FBU3dDLElBQUk7WUFDckNRLFFBQVFqQyxLQUFLLENBQUMsd0JBQXdCd0I7WUFDdEMsTUFBTSxJQUFJaEMsTUFBTSxDQUFDLGdDQUFnQyxFQUFFUCxTQUFTUSxNQUFNLEVBQUU7UUFDdEU7UUFFQSxNQUFNQyxPQUFPLE1BQU1ULFNBQVNVLElBQUk7UUFDaENzQyxRQUFRQyxHQUFHLENBQUMsNEJBQTRCeEM7UUFDeEMsT0FBT0E7SUFDVCxFQUFFLE9BQU9NLE9BQU87UUFDZGlDLFFBQVFqQyxLQUFLLENBQUMsa0NBQWtDQTtRQUNoRCxNQUFNQTtJQUNSO0FBQ0Y7QUFFQTs7OztDQUlDLEdBQ00sZUFBZXVFLGdDQUFnQ0MsTUFBYztJQUNsRSxJQUFJO1FBQ0YsTUFBTXZGLFdBQVcsTUFBTUMsTUFBTSxnRUFBZ0U7WUFDM0ZDLFFBQVE7WUFDUkMsU0FBUztnQkFBRSxnQkFBZ0I7WUFBbUI7WUFDOUNpQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQUVrRCxJQUFJRDtZQUFPO1FBQ3BDO1FBRUEsSUFBSXZGLFNBQVNNLEVBQUUsRUFBRTtZQUNmLE1BQU1tRixnQkFBZ0IsTUFBTXpGLFNBQVNVLElBQUk7WUFFekMsK0NBQStDO1lBQy9DLElBQUkrRSxpQkFBaUI5RSxNQUFNQyxPQUFPLENBQUM2RSxrQkFBa0JBLGNBQWM1RCxNQUFNLEdBQUcsR0FBRztnQkFDN0UsTUFBTTZELFdBQVdELGFBQWEsQ0FBQyxFQUFFO2dCQUNqQyxPQUFPO29CQUNMRSxTQUFTRCxTQUFTRixFQUFFO29CQUNwQkksY0FBY0YsU0FBU0UsWUFBWTtvQkFDbkNDLG9CQUFvQkgsU0FBU0csa0JBQWtCO29CQUMvQ0MsY0FBY0osU0FBU0ksWUFBWTtvQkFDbkNDLFlBQVlMLFNBQVNLLFVBQVU7b0JBQy9CQyxZQUFZTixTQUFTTSxVQUFVO29CQUMvQkMsVUFBVVAsU0FBU08sUUFBUTtvQkFDM0JDLGtCQUFrQlIsU0FBU1EsZ0JBQWdCO2dCQUM3QztZQUNGO1FBQ0Y7SUFDRixFQUFFLE9BQU9uRixPQUFPO1FBQ2RpQyxRQUFRakMsS0FBSyxDQUFDLDJDQUEyQ0E7SUFDM0Q7SUFDQSxPQUFPO0FBQ1Q7QUFFQTs7Ozs7Q0FLQyxHQUNNLGVBQWVvRiw2QkFBNkJDLE9BQVk7SUFDN0QsSUFBSTtRQUNGcEQsUUFBUUMsR0FBRyxDQUFDLDRDQUE0Q21ELFFBQVFDLFVBQVU7UUFDMUVyRCxRQUFRQyxHQUFHLENBQUMsdUJBQXVCO1lBQ2pDcUQsYUFBYUYsUUFBUUUsV0FBVztZQUNoQ0MsWUFBWUgsUUFBUUksZ0JBQWdCO1lBQ3BDQyxXQUFXTCxRQUFRSyxTQUFTO1lBQzVCQyxjQUFjTixRQUFRTSxZQUFZO1lBQ2xDQyxvQkFBb0JQLFFBQVFPLGtCQUFrQjtRQUNoRDtRQUVBLDhDQUE4QztRQUM5QyxNQUFNQyxrQkFBa0IsSUFBSWhIO1FBQzVCLE1BQU1pSCxlQUFlL0csV0FBVyxJQUFNOEcsZ0JBQWdCN0csS0FBSyxJQUFJLFFBQVEsb0JBQW9CO1FBRTNGLE1BQU0rRyxnQkFBZ0IsTUFBTTdHLE1BQU0sb0VBQW9FO1lBQ3BHQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQUUsZ0JBQWdCO1lBQW1CO1lBQzlDQyxRQUFRd0csZ0JBQWdCeEcsTUFBTTtRQUNoQztRQUVBQyxhQUFhd0c7UUFFYixJQUFJLENBQUNDLGNBQWN4RyxFQUFFLEVBQUU7WUFDckIwQyxRQUFRakMsS0FBSyxDQUFDLDRCQUE0QitGLGNBQWN0RyxNQUFNO1lBQzlELE9BQU87UUFDVDtRQUVBLE1BQU11RyxXQUFXLE1BQU1ELGNBQWNwRyxJQUFJO1FBQ3pDc0MsUUFBUUMsR0FBRyxDQUFDLENBQUMsMEJBQTBCLEVBQUU4RCxTQUFTbEYsTUFBTSxFQUFFO1FBRTFELHVFQUF1RTtRQUN2RSxNQUFNbUYsbUJBQW1CLElBQUlwSDtRQUM3QixNQUFNcUgsZ0JBQWdCbkgsV0FBVyxJQUFNa0gsaUJBQWlCakgsS0FBSyxJQUFJLFFBQVEsb0JBQW9CO1FBRTdGLE1BQU1tSCxpQkFBaUIsTUFBTWpILE1BQU0sMERBQTBEO1lBQzNGQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQUUsZ0JBQWdCO1lBQW1CO1lBQzlDQyxRQUFRNEcsaUJBQWlCNUcsTUFBTTtRQUNqQztRQUVBQyxhQUFhNEc7UUFFYixJQUFJLENBQUNDLGVBQWU1RyxFQUFFLEVBQUU7WUFDdEIwQyxRQUFRakMsS0FBSyxDQUFDLDZCQUE2Qm1HLGVBQWUxRyxNQUFNO1lBQ2hFLE9BQU87UUFDVDtRQUVBLE1BQU0yRyxZQUFZLE1BQU1ELGVBQWV4RyxJQUFJO1FBRTNDLDBDQUEwQztRQUMxQyxNQUFNMEcsZ0JBQWdCRCxVQUFVRSxJQUFJLENBQUMsQ0FBQ0M7WUFDcEMsTUFBTUMsWUFBWSxJQUFJM0QsS0FBSzBELE1BQU1mLFVBQVUsRUFBRWlCLFlBQVk7WUFDekQsTUFBTUMsY0FBYyxJQUFJN0QsS0FBS3dDLFFBQVFJLGdCQUFnQixFQUFFZ0IsWUFBWTtZQUNuRSxPQUFPRixNQUFNSSxLQUFLLEtBQUt0QixRQUFRRSxXQUFXLElBQUlpQixjQUFjRTtRQUM5RDtRQUVBLElBQUksQ0FBQ0wsZUFBZTtZQUNsQnBFLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0NtRCxRQUFRRSxXQUFXO1lBQ2pFLE9BQU87UUFDVDtRQUVBdEQsUUFBUUMsR0FBRyxDQUFDLDJCQUEyQm1FLGNBQWM1QixFQUFFLEVBQUU0QixjQUFjTSxLQUFLO1FBRTVFLHFFQUFxRTtRQUNyRSxNQUFNQyxrQkFBa0IsSUFBSS9IO1FBQzVCLE1BQU1nSSxlQUFlOUgsV0FBVyxJQUFNNkgsZ0JBQWdCNUgsS0FBSyxJQUFJLFFBQVEsb0JBQW9CO1FBRTNGLE1BQU04SCxnQkFBZ0IsTUFBTTVILE1BQU0sK0RBQStEO1lBQy9GQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQUUsZ0JBQWdCO1lBQW1CO1lBQzlDQyxRQUFRdUgsZ0JBQWdCdkgsTUFBTTtRQUNoQztRQUVBQyxhQUFhdUg7UUFFYixJQUFJLENBQUNDLGNBQWN2SCxFQUFFLEVBQUU7WUFDckIwQyxRQUFRakMsS0FBSyxDQUFDLDRCQUE0QjhHLGNBQWNySCxNQUFNO1lBQzlELE9BQU87UUFDVDtRQUVBLE1BQU1zSCxXQUFXLE1BQU1ELGNBQWNuSCxJQUFJO1FBRXpDLHlDQUF5QztRQUN6QyxNQUFNcUgsZUFBZUQsU0FBU1QsSUFBSSxDQUFDLENBQUNXLE9BQ2xDQSxLQUFLQyxVQUFVLEtBQUs3QixRQUFRSyxTQUFTLElBQ3JDdUIsS0FBS2xILElBQUksS0FBS3NGLFFBQVFLLFNBQVMsSUFDL0J1QixLQUFLTixLQUFLLEtBQUt0QixRQUFRSyxTQUFTO1FBR2xDLElBQUksQ0FBQ3NCLGNBQWM7WUFDakIvRSxRQUFRQyxHQUFHLENBQUMsaUNBQWlDbUQsUUFBUUssU0FBUztZQUM5RCxPQUFPO1FBQ1Q7UUFFQXpELFFBQVFDLEdBQUcsQ0FBQywwQkFBMEI4RSxhQUFhdkMsRUFBRSxFQUFFdUMsYUFBYUUsVUFBVSxJQUFJRixhQUFhakgsSUFBSTtRQUVuRyw0Q0FBNEM7UUFDNUMsTUFBTW9ILGdCQUFnQm5CLFNBQVNvQixNQUFNLENBQUMsQ0FBQ0MsT0FDckNBLEtBQUtDLFFBQVEsS0FBS2pCLGNBQWM1QixFQUFFLElBQUk0QyxLQUFLRSxPQUFPLEtBQUtQLGFBQWF2QyxFQUFFO1FBR3hFeEMsUUFBUUMsR0FBRyxDQUFDLENBQUMsU0FBUyxFQUFFaUYsY0FBY3JHLE1BQU0sQ0FBQywwQkFBMEIsRUFBRXVGLGNBQWM1QixFQUFFLENBQUMsUUFBUSxFQUFFdUMsYUFBYXZDLEVBQUUsRUFBRTtRQUVySCxJQUFJMEMsY0FBY3JHLE1BQU0sS0FBSyxHQUFHO1lBQzlCbUIsUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBTztRQUNUO1FBRUEsdUNBQXVDO1FBQ3ZDaUYsY0FBY0ssT0FBTyxDQUFDLENBQUNILE1BQVdJO1lBQ2hDeEYsUUFBUUMsR0FBRyxDQUFDLENBQUMsRUFBRSxFQUFFdUYsUUFBUSxFQUFFLE9BQU8sRUFBRUosS0FBSzVDLEVBQUUsQ0FBQyxHQUFHLEVBQUU0QyxLQUFLeEMsWUFBWSxJQUFJLGtCQUFrQixXQUFXLEVBQUV3QyxLQUFLckMsVUFBVSxFQUFFO1FBQ3hIO1FBRUEsbUNBQW1DO1FBQ25DLElBQUltQyxjQUFjckcsTUFBTSxLQUFLLEdBQUc7WUFDOUIsTUFBTXVHLE9BQU9GLGFBQWEsQ0FBQyxFQUFFO1lBQzdCbEYsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ21GLEtBQUs1QyxFQUFFLEVBQUU0QyxLQUFLeEMsWUFBWTtZQUN2RSxPQUFPLE1BQU1OLGdDQUFnQzhDLEtBQUs1QyxFQUFFO1FBQ3REO1FBRUEsOENBQThDO1FBRTlDLHVGQUF1RjtRQUN2RixNQUFNaUQsZ0JBQWdCQyxXQUFXdEMsUUFBUU0sWUFBWTtRQUNyRCxNQUFNaUMscUJBQXFCVCxjQUFjQyxNQUFNLENBQUMsQ0FBQ0M7WUFDL0MsTUFBTVEsWUFBWUYsV0FBV04sS0FBS3JDLFVBQVUsSUFBSXFDLEtBQUt0QyxZQUFZLElBQUk7WUFDckUseURBQXlEO1lBQ3pELE9BQU8rQyxLQUFLQyxHQUFHLENBQUNMLGdCQUFnQkcsY0FBYyxHQUFHLHVCQUF1QjtRQUMxRTtRQUVBLElBQUlELG1CQUFtQjlHLE1BQU0sS0FBSyxHQUFHO1lBQ25DLE1BQU11RyxPQUFPTyxrQkFBa0IsQ0FBQyxFQUFFO1lBQ2xDM0YsUUFBUUMsR0FBRyxDQUFDLCtCQUErQm1GLEtBQUs1QyxFQUFFLEVBQUU0QyxLQUFLeEMsWUFBWSxFQUFFLENBQUMsQ0FBQyxFQUFFd0MsS0FBS3JDLFVBQVUsQ0FBQyxDQUFDLENBQUM7WUFDN0YsT0FBTyxNQUFNVCxnQ0FBZ0M4QyxLQUFLNUMsRUFBRTtRQUN0RDtRQUVBLG1HQUFtRztRQUNuRyxNQUFNdUQsY0FBY2IsY0FBY0MsTUFBTSxDQUFDLENBQUNDLE9BQ3hDQSxLQUFLeEMsWUFBWSxJQUNqQndDLEtBQUt4QyxZQUFZLENBQUNvRCxJQUFJLE9BQU8sTUFDN0JaLEtBQUt4QyxZQUFZLEtBQUtRLFFBQVFLLFNBQVM7UUFHekMsSUFBSXNDLFlBQVlsSCxNQUFNLEtBQUssR0FBRztZQUM1QixNQUFNdUcsT0FBT1csV0FBVyxDQUFDLEVBQUU7WUFDM0IvRixRQUFRQyxHQUFHLENBQUMsK0JBQStCbUYsS0FBSzVDLEVBQUUsRUFBRTRDLEtBQUt4QyxZQUFZO1lBQ3JFLE9BQU8sTUFBTU4sZ0NBQWdDOEMsS0FBSzVDLEVBQUU7UUFDdEQ7UUFFQSxtRkFBbUY7UUFDbkYsSUFBSVksUUFBUU8sa0JBQWtCLEVBQUU7WUFDOUIsTUFBTXNDLGNBQWMsSUFBSXJGLEtBQUt3QyxRQUFRTyxrQkFBa0I7WUFDdkQsTUFBTXVDLG9CQUFvQmhCLGNBQWNDLE1BQU0sQ0FBQyxDQUFDQztnQkFDOUMsSUFBSSxDQUFDQSxLQUFLZSxVQUFVLEVBQUUsT0FBTztnQkFDN0IsTUFBTUMsV0FBVyxJQUFJeEYsS0FBS3dFLEtBQUtlLFVBQVU7Z0JBQ3pDLE1BQU1FLFdBQVdSLEtBQUtDLEdBQUcsQ0FBQ0csWUFBWUssT0FBTyxLQUFLRixTQUFTRSxPQUFPO2dCQUNsRSxrQkFBa0I7Z0JBQ2xCLE9BQU9ELFlBQVksS0FBSyxLQUFLLEtBQUs7WUFDcEM7WUFFQSxJQUFJSCxrQkFBa0JySCxNQUFNLEtBQUssR0FBRztnQkFDbEMsTUFBTXVHLE9BQU9jLGlCQUFpQixDQUFDLEVBQUU7Z0JBQ2pDbEcsUUFBUUMsR0FBRyxDQUFDLDhCQUE4Qm1GLEtBQUs1QyxFQUFFLEVBQUU0QyxLQUFLeEMsWUFBWTtnQkFDcEUsT0FBTyxNQUFNTixnQ0FBZ0M4QyxLQUFLNUMsRUFBRTtZQUN0RDtRQUNGO1FBRUEsK0RBQStEO1FBQy9ELE1BQU0rRCxjQUFjckIsY0FBY3NCLElBQUksQ0FBQyxDQUFDQyxHQUFRQztZQUM5QyxNQUFNQyxRQUFRLElBQUkvRixLQUFLNkYsRUFBRU4sVUFBVSxJQUFJLEdBQUdHLE9BQU87WUFDakQsTUFBTU0sUUFBUSxJQUFJaEcsS0FBSzhGLEVBQUVQLFVBQVUsSUFBSSxHQUFHRyxPQUFPO1lBQ2pELE9BQU9NLFFBQVFELE9BQU8sb0JBQW9CO1FBQzVDO1FBRUEsTUFBTXZCLE9BQU9tQixXQUFXLENBQUMsRUFBRTtRQUMzQnZHLFFBQVFDLEdBQUcsQ0FBQyx5Q0FBeUNtRixLQUFLNUMsRUFBRSxFQUFFNEMsS0FBS3hDLFlBQVk7UUFDL0UsT0FBTyxNQUFNTixnQ0FBZ0M4QyxLQUFLNUMsRUFBRTtJQUV0RCxFQUFFLE9BQU96RSxPQUFPO1FBQ2RpQyxRQUFRakMsS0FBSyxDQUFDLHFDQUFxQ0E7UUFDbkQsT0FBTztJQUNUO0FBQ0Y7QUFFQTs7Ozs7Q0FLQyxHQUNNLGVBQWU4SSx3QkFBd0I5RyxPQUFlLEVBQUUrRyxNQUFjO0lBQzNFLElBQUk7UUFDRixNQUFNOUosV0FBVyxNQUFNQyxNQUFNLG9FQUFvRTtZQUMvRkMsUUFBUTtZQUNSQyxTQUFTO2dCQUFFLGdCQUFnQjtZQUFtQjtRQUNoRDtRQUVBLElBQUlILFNBQVNNLEVBQUUsRUFBRTtZQUNmLE1BQU15RyxXQUFXLE1BQU0vRyxTQUFTVSxJQUFJO1lBQ3BDLE1BQU1xSixlQUFlaEQsU0FBU00sSUFBSSxDQUFDLENBQUNlLE9BQ2xDQSxLQUFLQyxRQUFRLEtBQUt0RixXQUFXcUYsS0FBS0UsT0FBTyxLQUFLd0I7WUFHaEQsSUFBSUMsY0FBYztnQkFDaEIsT0FBTztvQkFDTHBFLFNBQVNvRSxhQUFhdkUsRUFBRTtvQkFDeEJJLGNBQWNtRSxhQUFhbkUsWUFBWTtvQkFDdkNDLG9CQUFvQmtFLGFBQWFsRSxrQkFBa0I7b0JBQ25EQyxjQUFjaUUsYUFBYWpFLFlBQVk7b0JBQ3ZDQyxZQUFZZ0UsYUFBYWhFLFVBQVU7b0JBQ25DQyxZQUFZK0QsYUFBYS9ELFVBQVU7b0JBQ25DQyxVQUFVOEQsYUFBYTlELFFBQVE7b0JBQy9CQyxrQkFBa0I2RCxhQUFhN0QsZ0JBQWdCO2dCQUNqRDtZQUNGO1FBQ0Y7SUFDRixFQUFFLE9BQU9uRixPQUFPO1FBQ2RpQyxRQUFRakMsS0FBSyxDQUFDLGdDQUFnQ0E7SUFDaEQ7SUFDQSxPQUFPO0FBQ1Q7QUFFQTs7OztDQUlDLEdBQ00sZUFBZWlKLHlCQUF5QjdILFNBQWlCO0lBQzlELElBQUk7UUFDRixNQUFNbkMsV0FBVyxNQUFNQyxNQUFNLDZEQUE2RDtZQUN4RkMsUUFBUTtZQUNSQyxTQUFTO2dCQUFFLGdCQUFnQjtZQUFtQjtRQUNoRDtRQUVBLElBQUlILFNBQVNNLEVBQUUsRUFBRTtZQUNmLE1BQU0ySixjQUFjLE1BQU1qSyxTQUFTVSxJQUFJO1lBQ3ZDLE1BQU13SixVQUFVRCxZQUFZNUMsSUFBSSxDQUFDLENBQUM4QyxJQUFXQSxFQUFFOUQsVUFBVSxLQUFLbEU7WUFFOUQsSUFBSStILFNBQVM7Z0JBQ1gsT0FBTztvQkFDTEUsWUFBWUYsUUFBUUUsVUFBVTtvQkFDOUJDLHVCQUF1QkgsUUFBUUksY0FBYztvQkFDN0NDLGdCQUFnQkwsUUFBUUssY0FBYztvQkFDdENDLGNBQWNOLFFBQVFNLFlBQVk7b0JBQ2xDQyxnQkFBZ0JQLFFBQVFPLGNBQWM7Z0JBQ3hDO1lBQ0Y7UUFDRjtJQUNGLEVBQUUsT0FBTzFKLE9BQU87UUFDZGlDLFFBQVFqQyxLQUFLLENBQUMsbUNBQW1DQTtJQUNuRDtJQUNBLE9BQU87QUFDVDtBQUVBOzs7OztDQUtDLEdBQ00sZUFBZTJKLDJCQUEyQnZJLFNBQWlCLEVBQUV3SSxhQUFxQjtJQUN2RixJQUFJO1FBQ0YzSCxRQUFRQyxHQUFHLENBQUMsQ0FBQyxpQkFBaUIsRUFBRWQsVUFBVSxvQkFBb0IsRUFBRXdJLGVBQWU7UUFFL0UseUZBQXlGO1FBQ3pGLE1BQU0zSyxXQUFXLE1BQU1DLE1BQU0sdUNBQXVDO1lBQ2xFQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1lBQ0FpQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQ25CSDtnQkFDQXdJO1lBQ0Y7UUFDRjtRQUVBLElBQUksQ0FBQzNLLFNBQVNNLEVBQUUsRUFBRTtZQUNoQixNQUFNaUMsWUFBWSxNQUFNdkMsU0FBU3dDLElBQUk7WUFDckMsTUFBTSxJQUFJakMsTUFBTSxDQUFDLHlDQUF5QyxFQUFFUCxTQUFTUSxNQUFNLENBQUMsR0FBRyxFQUFFK0IsV0FBVztRQUM5RjtRQUVBLE9BQU8sTUFBTXZDLFNBQVNVLElBQUk7SUFDNUIsRUFBRSxPQUFPSyxPQUFZO1FBQ25CaUMsUUFBUWpDLEtBQUssQ0FBQywwQ0FBMENBO1FBQ3hELE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7O0NBSUMsR0FDTSxlQUFlNkosY0FBY0MsV0EwQm5DO0lBQ0MsSUFBSTtRQUNGLGtEQUFrRDtRQUNsRCxNQUFNN0ssV0FBVyxNQUFNQyxNQUFNLHdCQUF3QjtZQUNuREMsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBaUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDdUk7UUFDdkI7UUFFQSxJQUFJLENBQUM3SyxTQUFTTSxFQUFFLEVBQUU7WUFDaEIsTUFBTSxJQUFJQyxNQUFNLENBQUMsMkJBQTJCLEVBQUVQLFNBQVNRLE1BQU0sRUFBRTtRQUNqRTtRQUVBLE1BQU1DLE9BQU8sTUFBTVQsU0FBU1UsSUFBSTtRQUNoQyxPQUFPRDtJQUNULEVBQUUsT0FBT00sT0FBWTtRQUNuQixNQUFNQTtJQUNSO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdW1hXFxEZXNrdG9wXFxuaWJvZy1sYXRlc3RcXG5pYm9nXFxzZXJ2aWNlc1xcYm9va2luZ1NlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQm9va2luZyBzZXJ2aWNlIGZvciBoYW5kbGluZyBib29raW5nLXJlbGF0ZWQgQVBJIGNhbGxzXHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIEJvb2tpbmcge1xyXG4gIGJvb2tpbmdfaWQ6IG51bWJlcjtcclxuICBib29raW5nX3JlZjogc3RyaW5nO1xyXG4gIGJvb2tpbmdfc3RhdHVzOiBzdHJpbmc7XHJcbiAgdG90YWxfYW1vdW50OiBzdHJpbmc7XHJcbiAgcGF5bWVudF9tZXRob2Q6IHN0cmluZztcclxuICBwYXltZW50X3N0YXR1czogc3RyaW5nO1xyXG4gIHRlcm1zX2FjY2VwdGVkOiBib29sZWFuO1xyXG4gIGJvb2tpbmdfaXNfYWN0aXZlOiBib29sZWFuO1xyXG4gIGJvb2tpbmdfY3JlYXRlZF9hdDogc3RyaW5nO1xyXG4gIGJvb2tpbmdfdXBkYXRlZF9hdDogc3RyaW5nO1xyXG4gIGNhbmNlbGxlZF9hdDogc3RyaW5nIHwgbnVsbDtcclxuICBjb21wbGV0ZWRfYXQ6IHN0cmluZyB8IG51bGw7XHJcbiAgcGFyZW50X2lkOiBudW1iZXI7XHJcbiAgcGFyZW50X25hbWU6IHN0cmluZztcclxuICBwYXJlbnRfZW1haWw6IHN0cmluZztcclxuICBwYXJlbnRfYWRkaXRpb25hbF9waG9uZTogc3RyaW5nO1xyXG4gIHBhcmVudF9pc19hY3RpdmU6IGJvb2xlYW47XHJcbiAgcGFyZW50X2NyZWF0ZWRfYXQ6IHN0cmluZztcclxuICBwYXJlbnRfdXBkYXRlZF9hdDogc3RyaW5nO1xyXG4gIGNoaWxkX2lkOiBudW1iZXI7XHJcbiAgY2hpbGRfZnVsbF9uYW1lOiBzdHJpbmc7XHJcbiAgY2hpbGRfZGF0ZV9vZl9iaXJ0aDogc3RyaW5nO1xyXG4gIGNoaWxkX3NjaG9vbF9uYW1lOiBzdHJpbmc7XHJcbiAgY2hpbGRfZ2VuZGVyOiBzdHJpbmc7XHJcbiAgY2hpbGRfaXNfYWN0aXZlOiBib29sZWFuO1xyXG4gIGNoaWxkX2NyZWF0ZWRfYXQ6IHN0cmluZztcclxuICBjaGlsZF91cGRhdGVkX2F0OiBzdHJpbmc7XHJcbiAgZ2FtZV9uYW1lOiBzdHJpbmc7XHJcbiAgZ2FtZV9kZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIGdhbWVfbWluX2FnZTogbnVtYmVyO1xyXG4gIGdhbWVfbWF4X2FnZTogbnVtYmVyO1xyXG4gIGdhbWVfZHVyYXRpb25fbWludXRlczogbnVtYmVyO1xyXG4gIGdhbWVfY2F0ZWdvcmllczogc3RyaW5nW107XHJcbiAgZ2FtZV9pc19hY3RpdmU6IGJvb2xlYW47XHJcbiAgZ2FtZV9jcmVhdGVkX2F0OiBzdHJpbmc7XHJcbiAgZ2FtZV91cGRhdGVkX2F0OiBzdHJpbmc7XHJcbiAgZXZlbnRfdGl0bGU6IHN0cmluZztcclxuICBldmVudF9kZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIGV2ZW50X2V2ZW50X2RhdGU6IHN0cmluZztcclxuICBldmVudF9zdGF0dXM6IHN0cmluZztcclxuICBldmVudF9jcmVhdGVkX2F0OiBzdHJpbmc7XHJcbiAgZXZlbnRfdXBkYXRlZF9hdDogc3RyaW5nO1xyXG4gIHVzZXJfZnVsbF9uYW1lOiBzdHJpbmc7XHJcbiAgdXNlcl9lbWFpbDogc3RyaW5nO1xyXG4gIHVzZXJfcGhvbmU6IHN0cmluZztcclxuICB1c2VyX2NpdHlfaWQ6IG51bWJlcjtcclxuICB1c2VyX2FjY2VwdGVkX3Rlcm1zOiBib29sZWFuO1xyXG4gIHVzZXJfdGVybXNfYWNjZXB0ZWRfYXQ6IHN0cmluZyB8IG51bGw7XHJcbiAgdXNlcl9pc19hY3RpdmU6IGJvb2xlYW47XHJcbiAgdXNlcl9pc19sb2NrZWQ6IGJvb2xlYW47XHJcbiAgdXNlcl9sb2NrZWRfdW50aWw6IHN0cmluZyB8IG51bGw7XHJcbiAgdXNlcl9kZWFjdGl2YXRlZF9hdDogc3RyaW5nIHwgbnVsbDtcclxuICB1c2VyX2NyZWF0ZWRfYXQ6IHN0cmluZztcclxuICB1c2VyX3VwZGF0ZWRfYXQ6IHN0cmluZztcclxuICB1c2VyX2xhc3RfbG9naW5fYXQ6IHN0cmluZyB8IG51bGw7XHJcbiAgY2l0eV9uYW1lOiBzdHJpbmc7XHJcbiAgY2l0eV9zdGF0ZTogc3RyaW5nO1xyXG4gIGNpdHlfaXNfYWN0aXZlOiBib29sZWFuO1xyXG4gIGNpdHlfY3JlYXRlZF9hdDogc3RyaW5nO1xyXG4gIGNpdHlfdXBkYXRlZF9hdDogc3RyaW5nO1xyXG4gIHZlbnVlX25hbWU6IHN0cmluZztcclxuICB2ZW51ZV9hZGRyZXNzOiBzdHJpbmc7XHJcbiAgdmVudWVfY2FwYWNpdHk6IG51bWJlcjtcclxuICB2ZW51ZV9pc19hY3RpdmU6IGJvb2xlYW47XHJcbiAgdmVudWVfY3JlYXRlZF9hdDogc3RyaW5nO1xyXG4gIHZlbnVlX3VwZGF0ZWRfYXQ6IHN0cmluZztcclxufVxyXG5cclxuLy8gUGFnaW5hdGlvbiBpbnRlcmZhY2VcclxuZXhwb3J0IGludGVyZmFjZSBQYWdpbmF0aW9uUGFyYW1zIHtcclxuICBwYWdlPzogbnVtYmVyO1xyXG4gIGxpbWl0PzogbnVtYmVyO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFBhZ2luYXRlZEJvb2tpbmdzUmVzcG9uc2Uge1xyXG4gIGRhdGE6IEJvb2tpbmdbXTtcclxuICBwYWdpbmF0aW9uOiB7XHJcbiAgICBwYWdlOiBudW1iZXI7XHJcbiAgICBsaW1pdDogbnVtYmVyO1xyXG4gICAgdG90YWw6IG51bWJlcjtcclxuICAgIHRvdGFsUGFnZXM6IG51bWJlcjtcclxuICAgIGhhc05leHQ6IGJvb2xlYW47XHJcbiAgICBoYXNQcmV2OiBib29sZWFuO1xyXG4gIH07XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBHZXQgYWxsIGJvb2tpbmdzIHdpdGggZXJyb3IgaGFuZGxpbmcgYW5kIHRpbWVvdXRcclxuICogQHJldHVybnMgUHJvbWlzZSB3aXRoIGFycmF5IG9mIGJvb2tpbmdzXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0QWxsQm9va2luZ3MoKTogUHJvbWlzZTxCb29raW5nW10+IHtcclxuICB0cnkge1xyXG4gICAgLy8gVXNlIG91ciBpbnRlcm5hbCBBUEkgcm91dGUgdG8gYXZvaWQgQ09SUyBpc3N1ZXNcclxuICAgIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XHJcbiAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IGNvbnRyb2xsZXIuYWJvcnQoKSwgMzAwMDApOyAvLyAzMCBzZWNvbmQgdGltZW91dFxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYm9va2luZ3MvZ2V0LWFsbCcsIHtcclxuICAgICAgICBtZXRob2Q6IFwiR0VUXCIsXHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBzaWduYWw6IGNvbnRyb2xsZXIuc2lnbmFsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBBUEkgcmV0dXJuZWQgZXJyb3Igc3RhdHVzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgIC8vIEhhbmRsZSBib3RoIG9sZCBmb3JtYXQgKGFycmF5KSBhbmQgbmV3IHBhZ2luYXRlZCBmb3JtYXRcclxuICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZGF0YSkpIHtcclxuICAgICAgICByZXR1cm4gZGF0YTtcclxuICAgICAgfSBlbHNlIGlmIChkYXRhLmRhdGEgJiYgQXJyYXkuaXNBcnJheShkYXRhLmRhdGEpKSB7XHJcbiAgICAgICAgcmV0dXJuIGRhdGEuZGF0YTtcclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIFtdO1xyXG4gICAgfSBjYXRjaCAoZmV0Y2hFcnJvcjogYW55KSB7XHJcbiAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xyXG4gICAgICBpZiAoZmV0Y2hFcnJvci5uYW1lID09PSAnQWJvcnRFcnJvcicpIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJSZXF1ZXN0IHRpbWVkIG91dC4gVGhlIHNlcnZlciB0b29rIHRvbyBsb25nIHRvIHJlc3BvbmQuXCIpO1xyXG4gICAgICB9XHJcbiAgICAgIHRocm93IGZldGNoRXJyb3I7XHJcbiAgICB9XHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogR2V0IHBhZ2luYXRlZCBib29raW5ncyB3aXRoIGVycm9yIGhhbmRsaW5nIGFuZCB0aW1lb3V0XHJcbiAqIEBwYXJhbSBwYXJhbXMgUGFnaW5hdGlvbiBwYXJhbWV0ZXJzXHJcbiAqIEByZXR1cm5zIFByb21pc2Ugd2l0aCBwYWdpbmF0ZWQgYm9va2luZ3MgcmVzcG9uc2VcclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRQYWdpbmF0ZWRCb29raW5ncyhwYXJhbXM6IFBhZ2luYXRpb25QYXJhbXMgPSB7fSk6IFByb21pc2U8UGFnaW5hdGVkQm9va2luZ3NSZXNwb25zZT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCB7IHBhZ2UgPSAxLCBsaW1pdCA9IDEwMCB9ID0gcGFyYW1zO1xyXG5cclxuICAgIC8vIFVzZSBvdXIgaW50ZXJuYWwgQVBJIHJvdXRlIHRvIGF2b2lkIENPUlMgaXNzdWVzXHJcbiAgICBjb25zdCBjb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xyXG4gICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiBjb250cm9sbGVyLmFib3J0KCksIDMwMDAwKTsgLy8gMzAgc2Vjb25kIHRpbWVvdXRcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB1cmwgPSBuZXcgVVJMKCcvYXBpL2Jvb2tpbmdzL2dldC1hbGwnLCB3aW5kb3cubG9jYXRpb24ub3JpZ2luKTtcclxuICAgICAgdXJsLnNlYXJjaFBhcmFtcy5zZXQoJ3BhZ2UnLCBwYWdlLnRvU3RyaW5nKCkpO1xyXG4gICAgICB1cmwuc2VhcmNoUGFyYW1zLnNldCgnbGltaXQnLCBsaW1pdC50b1N0cmluZygpKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godXJsLnRvU3RyaW5nKCksIHtcclxuICAgICAgICBtZXRob2Q6IFwiR0VUXCIsXHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBzaWduYWw6IGNvbnRyb2xsZXIuc2lnbmFsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBBUEkgcmV0dXJuZWQgZXJyb3Igc3RhdHVzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgIC8vIEVuc3VyZSB3ZSBoYXZlIHRoZSBleHBlY3RlZCBwYWdpbmF0ZWQgZm9ybWF0XHJcbiAgICAgIGlmIChkYXRhLmRhdGEgJiYgZGF0YS5wYWdpbmF0aW9uKSB7XHJcbiAgICAgICAgcmV0dXJuIGRhdGE7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEZhbGxiYWNrIGZvciBvbGQgZm9ybWF0XHJcbiAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGEpKSB7XHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgIGRhdGEsXHJcbiAgICAgICAgICBwYWdpbmF0aW9uOiB7XHJcbiAgICAgICAgICAgIHBhZ2U6IDEsXHJcbiAgICAgICAgICAgIGxpbWl0OiBkYXRhLmxlbmd0aCxcclxuICAgICAgICAgICAgdG90YWw6IGRhdGEubGVuZ3RoLFxyXG4gICAgICAgICAgICB0b3RhbFBhZ2VzOiAxLFxyXG4gICAgICAgICAgICBoYXNOZXh0OiBmYWxzZSxcclxuICAgICAgICAgICAgaGFzUHJldjogZmFsc2VcclxuICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG4gICAgICB9XHJcblxyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIHJlc3BvbnNlIGZvcm1hdFwiKTtcclxuICAgIH0gY2F0Y2ggKGZldGNoRXJyb3I6IGFueSkge1xyXG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKTtcclxuICAgICAgaWYgKGZldGNoRXJyb3IubmFtZSA9PT0gJ0Fib3J0RXJyb3InKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiUmVxdWVzdCB0aW1lZCBvdXQuIFRoZSBzZXJ2ZXIgdG9vayB0b28gbG9uZyB0byByZXNwb25kLlwiKTtcclxuICAgICAgfVxyXG4gICAgICB0aHJvdyBmZXRjaEVycm9yO1xyXG4gICAgfVxyXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuLy8gSW50ZXJmYWNlIGZvciBzdGF0dXMgdXBkYXRlIHJlcXVlc3RcclxuZXhwb3J0IGludGVyZmFjZSBVcGRhdGVTdGF0dXNSZXF1ZXN0IHtcclxuICBib29raW5nX2lkOiBudW1iZXI7XHJcbiAgc3RhdHVzOiBzdHJpbmc7XHJcbn1cclxuXHJcbi8vIEludGVyZmFjZSBmb3Igc3RhdHVzIHVwZGF0ZSByZXNwb25zZVxyXG5leHBvcnQgaW50ZXJmYWNlIFVwZGF0ZVN0YXR1c1Jlc3BvbnNlIHtcclxuICBib29raW5nX2lkOiBudW1iZXI7XHJcbiAgYm9va2luZ19yZWY6IHN0cmluZztcclxuICB1c2VyX2lkOiBudW1iZXI7XHJcbiAgcGFyZW50X2lkOiBudW1iZXI7XHJcbiAgZXZlbnRfaWQ6IG51bWJlcjtcclxuICBzdGF0dXM6IHN0cmluZztcclxuICB0b3RhbF9hbW91bnQ6IHN0cmluZztcclxuICBwYXltZW50X21ldGhvZDogc3RyaW5nO1xyXG4gIHBheW1lbnRfc3RhdHVzOiBzdHJpbmc7XHJcbiAgdGVybXNfYWNjZXB0ZWQ6IGJvb2xlYW47XHJcbiAgaXNfYWN0aXZlOiBib29sZWFuO1xyXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcclxuICB1cGRhdGVkX2F0OiBzdHJpbmc7XHJcbiAgY2FuY2VsbGVkX2F0OiBzdHJpbmcgfCBudWxsO1xyXG4gIGNvbXBsZXRlZF9hdDogc3RyaW5nIHwgbnVsbDtcclxufVxyXG5cclxuLyoqXHJcbiAqIFVwZGF0ZSBib29raW5nIHN0YXR1c1xyXG4gKiBAcGFyYW0gYm9va2luZ0lkIEJvb2tpbmcgSURcclxuICogQHBhcmFtIHN0YXR1cyBOZXcgc3RhdHVzXHJcbiAqIEByZXR1cm5zIFByb21pc2Ugd2l0aCB1cGRhdGVkIGJvb2tpbmcgZGF0YVxyXG4gKi9cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZUJvb2tpbmdTdGF0dXMoYm9va2luZ0lkOiBudW1iZXIsIHN0YXR1czogc3RyaW5nKTogUHJvbWlzZTxVcGRhdGVTdGF0dXNSZXNwb25zZT4ge1xyXG4gIHRyeSB7XHJcbiAgICAvLyBVc2Ugb3VyIGludGVybmFsIEFQSSByb3V0ZSB0byBhdm9pZCBDT1JTIGlzc3Vlc1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9ib29raW5ncy91cGRhdGUtc3RhdHVzJywge1xyXG4gICAgICBtZXRob2Q6IFwiUE9TVFwiLFxyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICBib29raW5nSWQsXHJcbiAgICAgICAgc3RhdHVzXHJcbiAgICAgIH0pLFxyXG4gICAgfSk7XHJcblxyXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zdCBlcnJvclRleHQgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7XHJcbiAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSBgQVBJIHJldHVybmVkIGVycm9yIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YDtcclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gSlNPTi5wYXJzZShlcnJvclRleHQpO1xyXG4gICAgICAgIGlmIChlcnJvckRhdGEuZXJyb3IpIHtcclxuICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yRGF0YS5lcnJvcjtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGUpIHtcclxuICAgICAgICAvLyBJZiB3ZSBjYW4ndCBwYXJzZSB0aGUgZXJyb3IgYXMgSlNPTiwgdXNlIHRoZSBzdGF0dXMgY29kZVxyXG4gICAgICB9XHJcblxyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JNZXNzYWdlKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG5cclxuICAgIC8vIFRoZSBBUEkgcmV0dXJucyBhbiBhcnJheSB3aXRoIGEgc2luZ2xlIGJvb2tpbmcgb2JqZWN0XHJcbiAgICBpZiAoQXJyYXkuaXNBcnJheShkYXRhKSAmJiBkYXRhLmxlbmd0aCA+IDApIHtcclxuICAgICAgcmV0dXJuIGRhdGFbMF07XHJcbiAgICB9IGVsc2UgaWYgKCFBcnJheS5pc0FycmF5KGRhdGEpKSB7XHJcbiAgICAgIHJldHVybiBkYXRhO1xyXG4gICAgfVxyXG5cclxuICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgcmVzcG9uc2UgZm9ybWF0IGZyb20gc3RhdHVzIHVwZGF0ZSBBUElcIik7XHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogR2V0IGJvb2tpbmcgYnkgSURcclxuICogQHBhcmFtIGJvb2tpbmdJZCBCb29raW5nIElEXHJcbiAqIEByZXR1cm5zIFByb21pc2Ugd2l0aCBib29raW5nIGRhdGFcclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRCb29raW5nQnlJZChib29raW5nSWQ6IHN0cmluZyB8IG51bWJlcik6IFByb21pc2U8Qm9va2luZz4ge1xyXG4gIHRyeSB7XHJcbiAgICAvLyBDcmVhdGUgYW4gQWJvcnRDb250cm9sbGVyIGZvciB0aW1lb3V0XHJcbiAgICBjb25zdCBjb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xyXG4gICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiBjb250cm9sbGVyLmFib3J0KCksIDE1MDAwKTsgLy8gMTUgc2Vjb25kIHRpbWVvdXRcclxuXHJcbiAgICAvLyBVc2Ugb3VyIGludGVybmFsIEFQSSByb3V0ZSB0byBhdm9pZCBDT1JTIGlzc3Vlc1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9ib29raW5ncy9nZXQvJHtib29raW5nSWR9YCwge1xyXG4gICAgICBtZXRob2Q6IFwiR0VUXCIsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgfSxcclxuICAgICAgc2lnbmFsOiBjb250cm9sbGVyLnNpZ25hbCxcclxuICAgIH0pO1xyXG5cclxuICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xyXG5cclxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgLy8gSWYgaXQncyBhIDQwNCwgdGhlIGJvb2tpbmcgZG9lc24ndCBleGlzdFxyXG4gICAgICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDQpIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEJvb2tpbmcgd2l0aCBJRCAke2Jvb2tpbmdJZH0gbm90IGZvdW5kYCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEZvciBvdGhlciBlcnJvcnMsIHRocm93IGltbWVkaWF0ZWx5IHdpdGhvdXQgZmFsbGJhY2sgdG8gcHJldmVudCBsb29wc1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBib29raW5nOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgcmV0dXJuIGRhdGE7XHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgLy8gSGFuZGxlIHRpbWVvdXQgZXJyb3JzXHJcbiAgICBpZiAoZXJyb3IubmFtZSA9PT0gJ0Fib3J0RXJyb3InKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignUmVxdWVzdCB0aW1lb3V0IC0gdGhlIGJvb2tpbmcgc2VydmljZSBpcyB0YWtpbmcgdG9vIGxvbmcgdG8gcmVzcG9uZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFJlLXRocm93IHRoZSBlcnJvciB3aXRob3V0IGZhbGxiYWNrIHRvIHByZXZlbnQgaW5maW5pdGUgbG9vcHNcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuLy8gSW50ZXJmYWNlIGZvciBjb21wcmVoZW5zaXZlIHRpY2tldCBkZXRhaWxzIGJhc2VkIG9uIHRoZSBuZXcgQVBJIGZvcm1hdFxyXG5leHBvcnQgaW50ZXJmYWNlIFRpY2tldERldGFpbHMge1xyXG4gIGJvb2tpbmdfaWQ6IG51bWJlcjtcclxuICBib29raW5nX3JlZjogc3RyaW5nO1xyXG4gIGJvb2tpbmdfc3RhdHVzOiBzdHJpbmc7XHJcbiAgdG90YWxfYW1vdW50OiBzdHJpbmc7XHJcbiAgcGF5bWVudF9tZXRob2Q6IHN0cmluZztcclxuICBwYXltZW50X3N0YXR1czogc3RyaW5nO1xyXG4gIHRlcm1zX2FjY2VwdGVkOiBib29sZWFuO1xyXG4gIGJvb2tpbmdfYWN0aXZlOiBib29sZWFuO1xyXG4gIGJvb2tpbmdfY3JlYXRlZF9hdDogc3RyaW5nO1xyXG4gIGJvb2tpbmdfdXBkYXRlZF9hdDogc3RyaW5nO1xyXG4gIGNhbmNlbGxlZF9hdDogc3RyaW5nIHwgbnVsbDtcclxuICBjb21wbGV0ZWRfYXQ6IHN0cmluZyB8IG51bGw7XHJcbiAgcGFyZW50X2lkOiBudW1iZXI7XHJcbiAgcGFyZW50X25hbWU6IHN0cmluZztcclxuICBwYXJlbnRfZW1haWw6IHN0cmluZztcclxuICBhZGRpdGlvbmFsX3Bob25lOiBzdHJpbmc7XHJcbiAgcGFyZW50X2FjdGl2ZTogYm9vbGVhbjtcclxuICB1c2VyX2lkOiBudW1iZXI7XHJcbiAgdXNlcl9mdWxsX25hbWU6IHN0cmluZztcclxuICB1c2VyX2VtYWlsOiBzdHJpbmc7XHJcbiAgcGhvbmU6IHN0cmluZztcclxuICBlbWFpbF92ZXJpZmllZDogYm9vbGVhbjtcclxuICBwaG9uZV92ZXJpZmllZDogYm9vbGVhbjtcclxuICB1c2VyX2NpdHlfaWQ6IG51bWJlcjtcclxuICBhY2NlcHRlZF90ZXJtczogYm9vbGVhbjtcclxuICB0ZXJtc19hY2NlcHRlZF9hdDogc3RyaW5nIHwgbnVsbDtcclxuICB1c2VyX2FjdGl2ZTogYm9vbGVhbjtcclxuICBpc19sb2NrZWQ6IGJvb2xlYW47XHJcbiAgbG9ja2VkX3VudGlsOiBzdHJpbmcgfCBudWxsO1xyXG4gIGRlYWN0aXZhdGVkX2F0OiBzdHJpbmcgfCBudWxsO1xyXG4gIHVzZXJfY3JlYXRlZF9hdDogc3RyaW5nO1xyXG4gIHVzZXJfdXBkYXRlZF9hdDogc3RyaW5nO1xyXG4gIGxhc3RfbG9naW5fYXQ6IHN0cmluZyB8IG51bGw7XHJcbiAgY2hpbGRfaWQ6IG51bWJlcjtcclxuICBjaGlsZF9uYW1lOiBzdHJpbmc7XHJcbiAgZGF0ZV9vZl9iaXJ0aDogc3RyaW5nO1xyXG4gIGdlbmRlcjogc3RyaW5nO1xyXG4gIHNjaG9vbF9uYW1lOiBzdHJpbmc7XHJcbiAgY2hpbGRfYWN0aXZlOiBib29sZWFuO1xyXG4gIGV2ZW50X2lkOiBudW1iZXI7XHJcbiAgZXZlbnRfdGl0bGU6IHN0cmluZztcclxuICBldmVudF9kZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIGV2ZW50X2RhdGU6IHN0cmluZztcclxuICBldmVudF9zdGF0dXM6IHN0cmluZztcclxuICBldmVudF9jcmVhdGVkX2F0OiBzdHJpbmc7XHJcbiAgLy8gRXZlbnQgR2FtZSBTbG90IEluZm8gKGZyb20gZXZlbnRfZ2FtZXNfd2l0aF9zbG90cylcclxuICBldmVudF9nYW1lX3Nsb3RfaWQ6IG51bWJlcjtcclxuICBjdXN0b21fdGl0bGU6IHN0cmluZztcclxuICBjdXN0b21fZGVzY3JpcHRpb246IHN0cmluZztcclxuICBjdXN0b21fcHJpY2U6IHN0cmluZztcclxuICBzdGFydF90aW1lOiBzdHJpbmc7XHJcbiAgZW5kX3RpbWU6IHN0cmluZztcclxuICBzbG90X3ByaWNlOiBzdHJpbmc7XHJcbiAgbWF4X3BhcnRpY2lwYW50czogbnVtYmVyO1xyXG4gIHNsb3RfY3JlYXRlZF9hdDogc3RyaW5nO1xyXG4gIHNsb3RfdXBkYXRlZF9hdDogc3RyaW5nO1xyXG5cclxuICAvLyBTbG90IGRldGFpbHMgZnJvbSB0aGUgbmV3IEFQSSByZXNwb25zZVxyXG4gIHNsb3RfaWQ6IG51bWJlcjtcclxuICBzbG90X3RpdGxlOiBzdHJpbmc7XHJcbiAgc2xvdF9kZXNjcmlwdGlvbjogc3RyaW5nO1xyXG5cclxuICAvLyBMZWdhY3kgZ2FtZSBmaWVsZHMgKGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5KVxyXG4gIGdhbWVfaWQ/OiBudW1iZXI7XHJcbiAgZ2FtZV9uYW1lPzogc3RyaW5nO1xyXG4gIGdhbWVfZGVzY3JpcHRpb24/OiBzdHJpbmc7XHJcbiAgbWluX2FnZT86IG51bWJlcjtcclxuICBtYXhfYWdlPzogbnVtYmVyO1xyXG4gIGR1cmF0aW9uX21pbnV0ZXM/OiBudW1iZXI7XHJcbiAgY2F0ZWdvcmllcz86IHN0cmluZ1tdO1xyXG4gIGdhbWVfYWN0aXZlPzogYm9vbGVhbjtcclxuICBib29raW5nX2dhbWVfaWQ6IG51bWJlcjtcclxuICBnYW1lX3ByaWNlOiBzdHJpbmc7XHJcbiAgYXR0ZW5kYW5jZV9zdGF0dXM6IHN0cmluZztcclxuICBnYW1lX2Jvb2tpbmdfY3JlYXRlZF9hdDogc3RyaW5nO1xyXG5cclxuICAvLyBBZGRpdGlvbmFsIGZpZWxkcyB0aGF0IG1pZ2h0IGJlIHVzZWZ1bCBmb3Igc2xvdCBpZGVudGlmaWNhdGlvblxyXG4gIC8vIHNsb3RfaWQgaXMgbm93IGRlZmluZWQgYWJvdmUgaW4gdGhlIHNsb3QgZGV0YWlscyBzZWN0aW9uXHJcblxyXG4gIC8vIFZlbnVlLXJlbGF0ZWQgZmllbGRzIHJldHVybmVkIGJ5IHRoZSBBUElcclxuICB2ZW51ZV9pZDogbnVtYmVyO1xyXG4gIHZlbnVlX25hbWU6IHN0cmluZztcclxuICB2ZW51ZV9hZGRyZXNzOiBzdHJpbmc7XHJcbiAgdmVudWVfY2FwYWNpdHk6IG51bWJlcjtcclxuICB2ZW51ZV9hY3RpdmU6IGJvb2xlYW47XHJcbiAgdmVudWVfY3JlYXRlZF9hdDogc3RyaW5nO1xyXG4gIHZlbnVlX3VwZGF0ZWRfYXQ6IHN0cmluZztcclxuICBldmVudF9jaXR5X2lkOiBudW1iZXI7XHJcbiAgY2l0eV9uYW1lOiBzdHJpbmc7XHJcbiAgc3RhdGU6IHN0cmluZztcclxufVxyXG5cclxuLyoqXHJcbiAqIEdldCBldmVudCBkZXRhaWxzIHdpdGggdmVudWUgaW5mb3JtYXRpb24gYnkgZXZlbnQgSURcclxuICogQHBhcmFtIGV2ZW50SWQgRXZlbnQgSURcclxuICogQHJldHVybnMgUHJvbWlzZSB3aXRoIGV2ZW50IGRldGFpbHMgaW5jbHVkaW5nIHZlbnVlIGluZm9ybWF0aW9uXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0RXZlbnRXaXRoVmVudWVEZXRhaWxzKGV2ZW50SWQ6IG51bWJlcikge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zb2xlLmxvZygnRmV0Y2hpbmcgZXZlbnQgZGV0YWlscyB3aXRoIHZlbnVlIGluZm9ybWF0aW9uIGZvciBldmVudCBJRDonLCBldmVudElkKTtcclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL2V2ZW50cy9nZXQtd2l0aC1nYW1lc2AsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXHJcbiAgICAgIH0sXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZXZlbnRJZCB9KVxyXG4gICAgfSk7XHJcblxyXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBldmVudCBkZXRhaWxzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBldmVudERhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICBjb25zb2xlLmxvZygnRXZlbnQgZGV0YWlscyB3aXRoIHZlbnVlIGluZm9ybWF0aW9uOicsIGV2ZW50RGF0YSk7XHJcblxyXG4gICAgcmV0dXJuIGV2ZW50RGF0YTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgZXZlbnQgZGV0YWlscyB3aXRoIHZlbnVlIGluZm9ybWF0aW9uOicsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIENvbnZlcnRzIGJvb2tpbmcgcmVmZXJlbmNlcyBiZXR3ZWVuIGZvcm1hdHNcclxuICogQHBhcmFtIHJlZiBUaGUgYm9va2luZyByZWZlcmVuY2UgdG8gY29udmVydFxyXG4gKiBAcGFyYW0gdGFyZ2V0Rm9ybWF0IFRoZSB0YXJnZXQgZm9ybWF0IGZvciB0aGUgY29udmVyc2lvbiAoJ0InIG9yICdQUFQnKVxyXG4gKiBAcmV0dXJucyBUaGUgY29udmVydGVkIGJvb2tpbmcgcmVmZXJlbmNlXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gY29udmVydEJvb2tpbmdSZWZGb3JtYXQocmVmOiBzdHJpbmcsIHRhcmdldEZvcm1hdDogJ0InIHwgJ1BQVCcgPSAnUFBUJyk6IHN0cmluZyB7XHJcbiAgaWYgKCFyZWYpIHJldHVybiAnJztcclxuICBcclxuICAvLyBSZW1vdmUgcXVvdGVzIGlmIGl0J3MgYSBKU09OIHN0cmluZ1xyXG4gIGxldCBjbGVhblJlZiA9IHJlZjtcclxuICBpZiAoY2xlYW5SZWYuc3RhcnRzV2l0aCgnXCInKSAmJiBjbGVhblJlZi5lbmRzV2l0aCgnXCInKSkge1xyXG4gICAgY2xlYW5SZWYgPSBjbGVhblJlZi5zbGljZSgxLCAtMSk7XHJcbiAgfVxyXG5cclxuICAvLyBFeHRyYWN0IG51bWVyaWMgcGFydHMgYmFzZWQgb24gY3VycmVudCBmb3JtYXRcclxuICBsZXQgbnVtZXJpY1BhcnQgPSAnJztcclxuICBjb25zdCBjdXJyZW50RGF0ZSA9IG5ldyBEYXRlKCk7XHJcbiAgY29uc3QgeWVhciA9IGN1cnJlbnREYXRlLmdldEZ1bGxZZWFyKCkudG9TdHJpbmcoKS5zbGljZSgtMik7XHJcbiAgY29uc3QgbW9udGggPSAoY3VycmVudERhdGUuZ2V0TW9udGgoKSArIDEpLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKTtcclxuICBjb25zdCBkYXkgPSBjdXJyZW50RGF0ZS5nZXREYXRlKCkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpO1xyXG4gIFxyXG4gIGlmIChjbGVhblJlZi5zdGFydHNXaXRoKCdCJykpIHtcclxuICAgIC8vIEV4dHJhY3QgZnJvbSBCIGZvcm1hdCAoQjAwMDAxMjMpXHJcbiAgICBudW1lcmljUGFydCA9IGNsZWFuUmVmLnJlcGxhY2UoL15CKFxcZCspJC8sICckMScpO1xyXG4gICAgXHJcbiAgICBpZiAodGFyZ2V0Rm9ybWF0ID09PSAnQicpIHtcclxuICAgICAgLy8gQWxyZWFkeSBpbiBCIGZvcm1hdCwganVzdCBub3JtYWxpemVcclxuICAgICAgcmV0dXJuIGBCJHtudW1lcmljUGFydC5wYWRTdGFydCg3LCAnMCcpfWA7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBDb252ZXJ0IEIgLT4gUFBUIGZvcm1hdFxyXG4gICAgICAvLyBVc2UgY3VycmVudCBkYXRlICsgbnVtZXJpYyBwYXJ0IGFzIHRoZSBpZGVudGlmaWVyXHJcbiAgICAgIHJldHVybiBgUFBUJHt5ZWFyfSR7bW9udGh9JHtkYXl9JHtudW1lcmljUGFydC5zbGljZSgtMykucGFkU3RhcnQoMywgJzAnKX1gO1xyXG4gICAgfVxyXG4gIH0gZWxzZSBpZiAoY2xlYW5SZWYuc3RhcnRzV2l0aCgnUFBUJykpIHtcclxuICAgIC8vIEV4dHJhY3QgZnJvbSBQUFQgZm9ybWF0IChQUFRZWU1NRER4eHgpXHJcbiAgICAvLyBQUFQgZm9ybWF0IHR5cGljYWxseSBoYXMgZGF0ZSBlbWJlZGRlZCBpbiBpdFxyXG4gICAgY29uc29sZS5sb2coYENvbnZlcnRpbmcgUFBUIHJlZmVyZW5jZTogJHtjbGVhblJlZn1gKTtcclxuICAgIGNvbnNvbGUubG9nKGBQUFQgcmVmZXJlbmNlIGxlbmd0aDogJHtjbGVhblJlZi5sZW5ndGh9YCk7XHJcbiAgICBjb25zb2xlLmxvZyhgUFBUIHJlZmVyZW5jZSBjaGFyYWN0ZXJzOmAsIGNsZWFuUmVmLnNwbGl0KCcnKS5tYXAoYyA9PiBgJHtjfSgke2MuY2hhckNvZGVBdCgwKX0pYCkpO1xyXG5cclxuICAgIC8vIE1vcmUgZmxleGlibGUgcmVnZXggdGhhdCBoYW5kbGVzIHZhcmlvdXMgUFBUIGZvcm1hdHNcclxuICAgIGNvbnN0IHBwdE1hdGNoID0gY2xlYW5SZWYubWF0Y2goL15QUFQoXFxkezZ9KShcXGQrKSQvKTtcclxuICAgIGNvbnNvbGUubG9nKGBQUFQgcmVnZXggbWF0Y2ggcmVzdWx0OmAsIHBwdE1hdGNoKTtcclxuXHJcbiAgICBpZiAocHB0TWF0Y2gpIHtcclxuICAgICAgY29uc3QgZGF0ZVN0ciA9IHBwdE1hdGNoWzFdOyAvLyBZWU1NREQgcGFydFxyXG4gICAgICBjb25zdCBpZFBhcnQgPSBwcHRNYXRjaFsyXTsgIC8vIHh4eCBwYXJ0IChudW1lcmljIElEKVxyXG4gICAgICBjb25zb2xlLmxvZyhgUFBUIG1hdGNoZWQgLSBkYXRlU3RyOiAke2RhdGVTdHJ9LCBpZFBhcnQ6ICR7aWRQYXJ0fWApO1xyXG5cclxuICAgICAgaWYgKHRhcmdldEZvcm1hdCA9PT0gJ1BQVCcpIHtcclxuICAgICAgICAvLyBBbHJlYWR5IGluIFBQVCBmb3JtYXQsIHJldHVybiBhcy1pcyB0byBwcmVzZXJ2ZSBvcmlnaW5hbCBkYXRlXHJcbiAgICAgICAgY29uc29sZS5sb2coYFJldHVybmluZyBQUFQgYXMtaXM6ICR7Y2xlYW5SZWZ9YCk7XHJcbiAgICAgICAgcmV0dXJuIGNsZWFuUmVmO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIC8vIENvbnZlcnQgUFBUIC0+IEIgZm9ybWF0XHJcbiAgICAgICAgLy8gVXNlIHRoZSBudW1lcmljIHBhcnQgYXMgaXMsIHBhZCB0byA3IGRpZ2l0c1xyXG4gICAgICAgIHJldHVybiBgQiR7aWRQYXJ0LnBhZFN0YXJ0KDcsICcwJyl9YDtcclxuICAgICAgfVxyXG4gICAgfSBlbHNlIHtcclxuICAgICAgLy8gVHJ5IGFsdGVybmF0aXZlIHBhdHRlcm5zIGZvciBQUFQgcmVmZXJlbmNlc1xyXG4gICAgICBjb25zb2xlLmxvZyhgUHJpbWFyeSBQUFQgcmVnZXggZmFpbGVkLCB0cnlpbmcgYWx0ZXJuYXRpdmUgcGF0dGVybnMuLi5gKTtcclxuXHJcbiAgICAgIC8vIENoZWNrIGlmIGl0J3MgYSB2YWxpZCBQUFQgZm9ybWF0IHdpdGggYW55IG51bWJlciBvZiBkaWdpdHMgYWZ0ZXIgdGhlIGRhdGVcclxuICAgICAgY29uc3QgYWx0TWF0Y2ggPSBjbGVhblJlZi5tYXRjaCgvXlBQVChcXGQrKSQvKTtcclxuICAgICAgaWYgKGFsdE1hdGNoICYmIGFsdE1hdGNoWzFdLmxlbmd0aCA+PSA5KSB7IC8vIEF0IGxlYXN0IFlZTU1ERCArIDMgZGlnaXRzXHJcbiAgICAgICAgY29uc3QgZnVsbE51bWJlciA9IGFsdE1hdGNoWzFdO1xyXG4gICAgICAgIGNvbnN0IGRhdGVTdHIgPSBmdWxsTnVtYmVyLnN1YnN0cmluZygwLCA2KTsgLy8gRmlyc3QgNiBkaWdpdHMgYXMgZGF0ZVxyXG4gICAgICAgIGNvbnN0IGlkUGFydCA9IGZ1bGxOdW1iZXIuc3Vic3RyaW5nKDYpOyAgICAgLy8gUmVzdCBhcyBJRFxyXG5cclxuICAgICAgICBjb25zb2xlLmxvZyhgQWx0ZXJuYXRpdmUgUFBUIHBhdHRlcm4gbWF0Y2hlZCAtIGRhdGVTdHI6ICR7ZGF0ZVN0cn0sIGlkUGFydDogJHtpZFBhcnR9YCk7XHJcblxyXG4gICAgICAgIGlmICh0YXJnZXRGb3JtYXQgPT09ICdQUFQnKSB7XHJcbiAgICAgICAgICAvLyBBbHJlYWR5IGluIFBQVCBmb3JtYXQsIHJldHVybiBhcy1pcyB0byBwcmVzZXJ2ZSBvcmlnaW5hbCBkYXRlXHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhgUmV0dXJuaW5nIFBQVCBhcy1pczogJHtjbGVhblJlZn1gKTtcclxuICAgICAgICAgIHJldHVybiBjbGVhblJlZjtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgLy8gQ29udmVydCBQUFQgLT4gQiBmb3JtYXRcclxuICAgICAgICAgIHJldHVybiBgQiR7aWRQYXJ0LnBhZFN0YXJ0KDcsICcwJyl9YDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIExhc3QgcmVzb3J0IC0gaWYgdGFyZ2V0IGZvcm1hdCBpcyBQUFQgYW5kIGlucHV0IGlzIGFscmVhZHkgUFBULCBwcmVzZXJ2ZSBpdFxyXG4gICAgICBpZiAodGFyZ2V0Rm9ybWF0ID09PSAnUFBUJykge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKGBQUFQgcmVmZXJlbmNlIGNvbnNpZGVyZWQgbWFsZm9ybWVkIGJ1dCBwcmVzZXJ2aW5nIG9yaWdpbmFsOiAke2NsZWFuUmVmfWApO1xyXG4gICAgICAgIHJldHVybiBjbGVhblJlZjsgLy8gUHJlc2VydmUgb3JpZ2luYWwgUFBUIHJlZmVyZW5jZSB0byBhdm9pZCBkYXRlIGNoYW5nZXNcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyBPbmx5IGNvbnZlcnQgdG8gQiBmb3JtYXQgaWYgZXhwbGljaXRseSByZXF1ZXN0ZWRcclxuICAgICAgICBjb25zb2xlLmxvZyhgUFBUIHJlZmVyZW5jZSBjb25zaWRlcmVkIG1hbGZvcm1lZCwgY29udmVydGluZyB0byBCIGZvcm1hdGApO1xyXG4gICAgICAgIG51bWVyaWNQYXJ0ID0gY2xlYW5SZWYucmVwbGFjZSgvXFxEL2csICcnKTtcclxuICAgICAgICBjb25zdCBmYWxsYmFja1Jlc3VsdCA9IGBCJHtudW1lcmljUGFydC5zbGljZSgtNykucGFkU3RhcnQoNywgJzAnKX1gO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKGBGYWxsYmFjayByZXN1bHQ6ICR7ZmFsbGJhY2tSZXN1bHR9YCk7XHJcbiAgICAgICAgcmV0dXJuIGZhbGxiYWNrUmVzdWx0O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSBlbHNlIHtcclxuICAgIC8vIFVua25vd24gZm9ybWF0LCBleHRyYWN0IGFueSBudW1lcmljIHBhcnRzXHJcbiAgICBudW1lcmljUGFydCA9IGNsZWFuUmVmLnJlcGxhY2UoL1xcRC9nLCAnJyk7XHJcbiAgICByZXR1cm4gdGFyZ2V0Rm9ybWF0ID09PSAnQicgPyBcclxuICAgICAgYEIke251bWVyaWNQYXJ0LnNsaWNlKC03KS5wYWRTdGFydCg3LCAnMCcpfWAgOiBcclxuICAgICAgYFBQVCR7eWVhcn0ke21vbnRofSR7ZGF5fSR7bnVtZXJpY1BhcnQuc2xpY2UoLTMpLnBhZFN0YXJ0KDMsICcwJyl9YDtcclxuICB9XHJcbn1cclxuXHJcbi8vIE5ldyBmdW5jdGlvbiB0byBmZXRjaCBkZXRhaWxlZCB0aWNrZXQgaW5mb3JtYXRpb24gdXNpbmcgYm9va2luZyByZWZlcmVuY2VcclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFRpY2tldERldGFpbHMoYm9va2luZ1JlZjogc3RyaW5nKTogUHJvbWlzZTxUaWNrZXREZXRhaWxzW10+IHtcclxuICB0cnkge1xyXG4gICAgY29uc29sZS5sb2coJ0ZldGNoaW5nIHRpY2tldCBkZXRhaWxzIHdpdGggYm9va2luZyByZWZlcmVuY2U6JywgYm9va2luZ1JlZik7XHJcbiAgICBcclxuICAgIC8vIEFQSSBleHBlY3RzIGJvb2tpbmdfcmVmX2lkIGluIFBQVCBmb3JtYXQgZm9yIHRoaXMgc3BlY2lmaWMgZW5kcG9pbnRcclxuICAgIC8vIElmIGFscmVhZHkgaW4gUFBUIGZvcm1hdCwgdXNlIGFzLWlzLiBPdGhlcndpc2UgY29udmVydCBmcm9tIEIgZm9ybWF0LlxyXG4gICAgbGV0IGZvcm1hdHRlZFJlZiA9IGJvb2tpbmdSZWY7XHJcbiAgICBpZiAoIWJvb2tpbmdSZWYuc3RhcnRzV2l0aCgnUFBUJykpIHtcclxuICAgICAgZm9ybWF0dGVkUmVmID0gY29udmVydEJvb2tpbmdSZWZGb3JtYXQoYm9va2luZ1JlZiwgJ1BQVCcpO1xyXG4gICAgICBjb25zb2xlLmxvZyhgQ29udmVydGVkIGJvb2tpbmcgcmVmZXJlbmNlIHRvIEFQSSBmb3JtYXQ6ICR7Ym9va2luZ1JlZn0gLT4gJHtmb3JtYXR0ZWRSZWZ9YCk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLmxvZyhgQm9va2luZyByZWZlcmVuY2UgYWxyZWFkeSBpbiBQUFQgZm9ybWF0OiAke2Jvb2tpbmdSZWZ9YCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIFN0cmlwIGFueSBKU09OIGZvcm1hdHRpbmcgaWYgaXQgd2FzIHN0b3JlZCBhcyBKU09OIHN0cmluZ1xyXG4gICAgaWYgKGZvcm1hdHRlZFJlZi5zdGFydHNXaXRoKCdcIicpICYmIGZvcm1hdHRlZFJlZi5lbmRzV2l0aCgnXCInKSkge1xyXG4gICAgICBmb3JtYXR0ZWRSZWYgPSBmb3JtYXR0ZWRSZWYuc2xpY2UoMSwgLTEpO1xyXG4gICAgICBjb25zb2xlLmxvZygnU3RyaXBwZWQgSlNPTiBxdW90ZXMgZnJvbSBib29raW5nIHJlZmVyZW5jZTonLCBmb3JtYXR0ZWRSZWYpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBjb25zb2xlLmxvZygnTWFraW5nIEFQSSBjYWxsIHdpdGggZm9ybWF0dGVkIGJvb2tpbmcgcmVmZXJlbmNlOicsIGZvcm1hdHRlZFJlZik7XHJcbiAgICBcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJ2h0dHBzOi8vYWkuYWx2aW9uZ3MuY29tL3dlYmhvb2svdjEvbmlib2cvdGlja2VjdC9ib29raW5nX3JlZi9kZXRhaWxzJywge1xyXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcclxuICAgICAgfSxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgIGJvb2tpbmdfcmVmX2lkOiBmb3JtYXR0ZWRSZWYgIC8vIFVzaW5nIGV4YWN0IHBhcmFtZXRlciBuYW1lIGV4cGVjdGVkIGJ5IHRoZSBBUElcclxuICAgICAgfSlcclxuICAgIH0pO1xyXG5cclxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgY29uc29sZS5lcnJvcihgQVBJIHJldHVybmVkIGVycm9yIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcclxuICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZXNwb25zZSBib2R5OicsIGVycm9yVGV4dCk7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHRpY2tldCBkZXRhaWxzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgY29uc29sZS5sb2coJ1JlY2VpdmVkIHRpY2tldCBkZXRhaWxzOicsIGRhdGEpO1xyXG4gICAgcmV0dXJuIGRhdGE7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHRpY2tldCBkZXRhaWxzOicsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEdldCBldmVudCBnYW1lIHNsb3QgZGV0YWlscyBieSBzbG90IElEIChwcmVmZXJyZWQgbWV0aG9kKVxyXG4gKiBAcGFyYW0gc2xvdElkIFNsb3QgSURcclxuICogQHJldHVybnMgUHJvbWlzZSB3aXRoIHNsb3QgZGV0YWlscyBvciBudWxsXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0RXZlbnRHYW1lU2xvdERldGFpbHNCeVNsb3RJZChzbG90SWQ6IG51bWJlcikge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCdodHRwczovL2FpLmFsdmlvbmdzLmNvbS93ZWJob29rL3YxL25pYm9nL2V2ZW50LWdhbWUtc2xvdC9nZXQnLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBpZDogc2xvdElkIH0pXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAocmVzcG9uc2Uub2spIHtcclxuICAgICAgY29uc3Qgc2xvdERhdGFBcnJheSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgIC8vIFRoZSBBUEkgcmV0dXJucyBhbiBhcnJheSwgZ2V0IHRoZSBmaXJzdCBpdGVtXHJcbiAgICAgIGlmIChzbG90RGF0YUFycmF5ICYmIEFycmF5LmlzQXJyYXkoc2xvdERhdGFBcnJheSkgJiYgc2xvdERhdGFBcnJheS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgY29uc3Qgc2xvdERhdGEgPSBzbG90RGF0YUFycmF5WzBdO1xyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICBzbG90X2lkOiBzbG90RGF0YS5pZCxcclxuICAgICAgICAgIGN1c3RvbV90aXRsZTogc2xvdERhdGEuY3VzdG9tX3RpdGxlLFxyXG4gICAgICAgICAgY3VzdG9tX2Rlc2NyaXB0aW9uOiBzbG90RGF0YS5jdXN0b21fZGVzY3JpcHRpb24sXHJcbiAgICAgICAgICBjdXN0b21fcHJpY2U6IHNsb3REYXRhLmN1c3RvbV9wcmljZSxcclxuICAgICAgICAgIHNsb3RfcHJpY2U6IHNsb3REYXRhLnNsb3RfcHJpY2UsXHJcbiAgICAgICAgICBzdGFydF90aW1lOiBzbG90RGF0YS5zdGFydF90aW1lLFxyXG4gICAgICAgICAgZW5kX3RpbWU6IHNsb3REYXRhLmVuZF90aW1lLFxyXG4gICAgICAgICAgbWF4X3BhcnRpY2lwYW50czogc2xvdERhdGEubWF4X3BhcnRpY2lwYW50cyxcclxuICAgICAgICB9O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHNsb3QgZGV0YWlscyBieSBzbG90IElEOicsIGVycm9yKTtcclxuICB9XHJcbiAgcmV0dXJuIG51bGw7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBGaW5kIHRoZSBtb3N0IGxpa2VseSBzbG90IGZvciBhIGJvb2tpbmcgYmFzZWQgb24gZXZlbnQgYW5kIGdhbWUgZGV0YWlsc1xyXG4gKiBUaGlzIGlzIGEgd29ya2Fyb3VuZCBmb3Igd2hlbiBib29raW5nX2dhbWVzIGRhdGEgaXMgbm90IGF2YWlsYWJsZVxyXG4gKiBAcGFyYW0gYm9va2luZyBCb29raW5nIGRhdGFcclxuICogQHJldHVybnMgUHJvbWlzZSB3aXRoIHNsb3QgZGV0YWlscyBvciBudWxsXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZmluZE1vc3RMaWtlbHlTbG90Rm9yQm9va2luZyhib29raW5nOiBhbnkpIHtcclxuICB0cnkge1xyXG4gICAgY29uc29sZS5sb2coJ/CflI0gRmluZGluZyBtb3N0IGxpa2VseSBzbG90IGZvciBib29raW5nOicsIGJvb2tpbmcuYm9va2luZ19pZCk7XHJcbiAgICBjb25zb2xlLmxvZygn8J+TiyBCb29raW5nIGRldGFpbHM6Jywge1xyXG4gICAgICBldmVudF90aXRsZTogYm9va2luZy5ldmVudF90aXRsZSxcclxuICAgICAgZXZlbnRfZGF0ZTogYm9va2luZy5ldmVudF9ldmVudF9kYXRlLFxyXG4gICAgICBnYW1lX25hbWU6IGJvb2tpbmcuZ2FtZV9uYW1lLFxyXG4gICAgICB0b3RhbF9hbW91bnQ6IGJvb2tpbmcudG90YWxfYW1vdW50LFxyXG4gICAgICBib29raW5nX2NyZWF0ZWRfYXQ6IGJvb2tpbmcuYm9va2luZ19jcmVhdGVkX2F0XHJcbiAgICB9KTtcclxuXHJcbiAgICAvLyBHZXQgYWxsIHNsb3RzIGZpcnN0IHdpdGggdGltZW91dCBwcm90ZWN0aW9uXHJcbiAgICBjb25zdCBzbG90c0NvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XHJcbiAgICBjb25zdCBzbG90c1RpbWVvdXQgPSBzZXRUaW1lb3V0KCgpID0+IHNsb3RzQ29udHJvbGxlci5hYm9ydCgpLCAxMDAwMCk7IC8vIDEwIHNlY29uZCB0aW1lb3V0XHJcblxyXG4gICAgY29uc3Qgc2xvdHNSZXNwb25zZSA9IGF3YWl0IGZldGNoKCdodHRwczovL2FpLmFsdmlvbmdzLmNvbS93ZWJob29rL3YxL25pYm9nL2V2ZW50LWdhbWUtc2xvdC9nZXQtYWxsJywge1xyXG4gICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcclxuICAgICAgc2lnbmFsOiBzbG90c0NvbnRyb2xsZXIuc2lnbmFsXHJcbiAgICB9KTtcclxuXHJcbiAgICBjbGVhclRpbWVvdXQoc2xvdHNUaW1lb3V0KTtcclxuXHJcbiAgICBpZiAoIXNsb3RzUmVzcG9uc2Uub2spIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIEZhaWxlZCB0byBmZXRjaCBzbG90czonLCBzbG90c1Jlc3BvbnNlLnN0YXR1cyk7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGFsbFNsb3RzID0gYXdhaXQgc2xvdHNSZXNwb25zZS5qc29uKCk7XHJcbiAgICBjb25zb2xlLmxvZyhg8J+TiiBUb3RhbCBzbG90cyBhdmFpbGFibGU6ICR7YWxsU2xvdHMubGVuZ3RofWApO1xyXG5cclxuICAgIC8vIEdldCBhbGwgZXZlbnRzIHRvIGZpbmQgdGhlIG1hdGNoaW5nIGV2ZW50IElEIHdpdGggdGltZW91dCBwcm90ZWN0aW9uXHJcbiAgICBjb25zdCBldmVudHNDb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xyXG4gICAgY29uc3QgZXZlbnRzVGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4gZXZlbnRzQ29udHJvbGxlci5hYm9ydCgpLCAxMDAwMCk7IC8vIDEwIHNlY29uZCB0aW1lb3V0XHJcblxyXG4gICAgY29uc3QgZXZlbnRzUmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnaHR0cHM6Ly9haS5hbHZpb25ncy5jb20vd2ViaG9vay92MS9uaWJvZy9ldmVudC9nZXQtYWxsJywge1xyXG4gICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcclxuICAgICAgc2lnbmFsOiBldmVudHNDb250cm9sbGVyLnNpZ25hbFxyXG4gICAgfSk7XHJcblxyXG4gICAgY2xlYXJUaW1lb3V0KGV2ZW50c1RpbWVvdXQpO1xyXG5cclxuICAgIGlmICghZXZlbnRzUmVzcG9uc2Uub2spIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIEZhaWxlZCB0byBmZXRjaCBldmVudHM6JywgZXZlbnRzUmVzcG9uc2Uuc3RhdHVzKTtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgYWxsRXZlbnRzID0gYXdhaXQgZXZlbnRzUmVzcG9uc2UuanNvbigpO1xyXG5cclxuICAgIC8vIEZpbmQgdGhlIGV2ZW50IHRoYXQgbWF0Y2hlcyB0aGUgYm9va2luZ1xyXG4gICAgY29uc3QgbWF0Y2hpbmdFdmVudCA9IGFsbEV2ZW50cy5maW5kKChldmVudDogYW55KSA9PiB7XHJcbiAgICAgIGNvbnN0IGV2ZW50RGF0ZSA9IG5ldyBEYXRlKGV2ZW50LmV2ZW50X2RhdGUpLnRvRGF0ZVN0cmluZygpO1xyXG4gICAgICBjb25zdCBib29raW5nRGF0ZSA9IG5ldyBEYXRlKGJvb2tpbmcuZXZlbnRfZXZlbnRfZGF0ZSkudG9EYXRlU3RyaW5nKCk7XHJcbiAgICAgIHJldHVybiBldmVudC50aXRsZSA9PT0gYm9va2luZy5ldmVudF90aXRsZSAmJiBldmVudERhdGUgPT09IGJvb2tpbmdEYXRlO1xyXG4gICAgfSk7XHJcblxyXG4gICAgaWYgKCFtYXRjaGluZ0V2ZW50KSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfinYwgTm8gbWF0Y2hpbmcgZXZlbnQgZm91bmQgZm9yOicsIGJvb2tpbmcuZXZlbnRfdGl0bGUpO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuXHJcbiAgICBjb25zb2xlLmxvZygn4pyFIEZvdW5kIG1hdGNoaW5nIGV2ZW50OicsIG1hdGNoaW5nRXZlbnQuaWQsIG1hdGNoaW5nRXZlbnQudGl0bGUpO1xyXG5cclxuICAgIC8vIEdldCBhbGwgZ2FtZXMgdG8gZmluZCB0aGUgbWF0Y2hpbmcgZ2FtZSBJRCB3aXRoIHRpbWVvdXQgcHJvdGVjdGlvblxyXG4gICAgY29uc3QgZ2FtZXNDb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xyXG4gICAgY29uc3QgZ2FtZXNUaW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiBnYW1lc0NvbnRyb2xsZXIuYWJvcnQoKSwgMTAwMDApOyAvLyAxMCBzZWNvbmQgdGltZW91dFxyXG5cclxuICAgIGNvbnN0IGdhbWVzUmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnaHR0cHM6Ly9haS5hbHZpb25ncy5jb20vd2ViaG9vay92MS9uaWJvZy9iYWJ5LWdhbWVzL2dldC1hbGwnLCB7XHJcbiAgICAgIG1ldGhvZDogJ0dFVCcsXHJcbiAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxyXG4gICAgICBzaWduYWw6IGdhbWVzQ29udHJvbGxlci5zaWduYWxcclxuICAgIH0pO1xyXG5cclxuICAgIGNsZWFyVGltZW91dChnYW1lc1RpbWVvdXQpO1xyXG5cclxuICAgIGlmICghZ2FtZXNSZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIGZldGNoIGdhbWVzOicsIGdhbWVzUmVzcG9uc2Uuc3RhdHVzKTtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgYWxsR2FtZXMgPSBhd2FpdCBnYW1lc1Jlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAvLyBGaW5kIHRoZSBnYW1lIHRoYXQgbWF0Y2hlcyB0aGUgYm9va2luZ1xyXG4gICAgY29uc3QgbWF0Y2hpbmdHYW1lID0gYWxsR2FtZXMuZmluZCgoZ2FtZTogYW55KSA9PlxyXG4gICAgICBnYW1lLmdhbWVfdGl0bGUgPT09IGJvb2tpbmcuZ2FtZV9uYW1lIHx8XHJcbiAgICAgIGdhbWUubmFtZSA9PT0gYm9va2luZy5nYW1lX25hbWUgfHxcclxuICAgICAgZ2FtZS50aXRsZSA9PT0gYm9va2luZy5nYW1lX25hbWVcclxuICAgICk7XHJcblxyXG4gICAgaWYgKCFtYXRjaGluZ0dhbWUpIHtcclxuICAgICAgY29uc29sZS5sb2coJ+KdjCBObyBtYXRjaGluZyBnYW1lIGZvdW5kIGZvcjonLCBib29raW5nLmdhbWVfbmFtZSk7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnNvbGUubG9nKCfinIUgRm91bmQgbWF0Y2hpbmcgZ2FtZTonLCBtYXRjaGluZ0dhbWUuaWQsIG1hdGNoaW5nR2FtZS5nYW1lX3RpdGxlIHx8IG1hdGNoaW5nR2FtZS5uYW1lKTtcclxuXHJcbiAgICAvLyBGaW5kIHNsb3RzIHRoYXQgbWF0Y2ggYm90aCBldmVudCBhbmQgZ2FtZVxyXG4gICAgY29uc3QgbWF0Y2hpbmdTbG90cyA9IGFsbFNsb3RzLmZpbHRlcigoc2xvdDogYW55KSA9PlxyXG4gICAgICBzbG90LmV2ZW50X2lkID09PSBtYXRjaGluZ0V2ZW50LmlkICYmIHNsb3QuZ2FtZV9pZCA9PT0gbWF0Y2hpbmdHYW1lLmlkXHJcbiAgICApO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKGDwn46vIEZvdW5kICR7bWF0Y2hpbmdTbG90cy5sZW5ndGh9IG1hdGNoaW5nIHNsb3RzIGZvciBldmVudCAke21hdGNoaW5nRXZlbnQuaWR9ICsgZ2FtZSAke21hdGNoaW5nR2FtZS5pZH1gKTtcclxuXHJcbiAgICBpZiAobWF0Y2hpbmdTbG90cy5sZW5ndGggPT09IDApIHtcclxuICAgICAgY29uc29sZS5sb2coJ+KdjCBObyBtYXRjaGluZyBzbG90cyBmb3VuZCcpO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuXHJcbiAgICAvLyBMb2cgYWxsIG1hdGNoaW5nIHNsb3RzIGZvciBkZWJ1Z2dpbmdcclxuICAgIG1hdGNoaW5nU2xvdHMuZm9yRWFjaCgoc2xvdDogYW55LCBpbmRleDogbnVtYmVyKSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKGAgICR7aW5kZXggKyAxfS4gU2xvdCAke3Nsb3QuaWR9OiBcIiR7c2xvdC5jdXN0b21fdGl0bGUgfHwgJ05vIGN1c3RvbSB0aXRsZSd9XCIgLSBQcmljZTogJHtzbG90LnNsb3RfcHJpY2V9YCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICAvLyBJZiB0aGVyZSdzIG9ubHkgb25lIHNsb3QsIHVzZSBpdFxyXG4gICAgaWYgKG1hdGNoaW5nU2xvdHMubGVuZ3RoID09PSAxKSB7XHJcbiAgICAgIGNvbnN0IHNsb3QgPSBtYXRjaGluZ1Nsb3RzWzBdO1xyXG4gICAgICBjb25zb2xlLmxvZygn4pyFIFVzaW5nIHNpbmdsZSBtYXRjaGluZyBzbG90OicsIHNsb3QuaWQsIHNsb3QuY3VzdG9tX3RpdGxlKTtcclxuICAgICAgcmV0dXJuIGF3YWl0IGdldEV2ZW50R2FtZVNsb3REZXRhaWxzQnlTbG90SWQoc2xvdC5pZCk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gSU1QUk9WRUQgTUFUQ0hJTkcgTE9HSUMgZm9yIG11bHRpcGxlIHNsb3RzOlxyXG5cclxuICAgIC8vIDEuIFRyeSB0byBtYXRjaCBieSBwcmljZSAoYm9va2luZyB0b3RhbF9hbW91bnQgc2hvdWxkIG1hdGNoIHNsb3RfcHJpY2UgKyBhbnkgYWRkb25zKVxyXG4gICAgY29uc3QgYm9va2luZ0Ftb3VudCA9IHBhcnNlRmxvYXQoYm9va2luZy50b3RhbF9hbW91bnQpO1xyXG4gICAgY29uc3QgcHJpY2VNYXRjaGluZ1Nsb3RzID0gbWF0Y2hpbmdTbG90cy5maWx0ZXIoKHNsb3Q6IGFueSkgPT4ge1xyXG4gICAgICBjb25zdCBzbG90UHJpY2UgPSBwYXJzZUZsb2F0KHNsb3Quc2xvdF9wcmljZSB8fCBzbG90LmN1c3RvbV9wcmljZSB8fCAnMCcpO1xyXG4gICAgICAvLyBBbGxvdyBmb3Igc21hbGwgZGlmZmVyZW5jZXMgZHVlIHRvIGFkZG9ucywgdGF4ZXMsIGV0Yy5cclxuICAgICAgcmV0dXJuIE1hdGguYWJzKGJvb2tpbmdBbW91bnQgLSBzbG90UHJpY2UpIDw9IDI7IC8vIFdpdGhpbiAkMiBkaWZmZXJlbmNlXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAocHJpY2VNYXRjaGluZ1Nsb3RzLmxlbmd0aCA9PT0gMSkge1xyXG4gICAgICBjb25zdCBzbG90ID0gcHJpY2VNYXRjaGluZ1Nsb3RzWzBdO1xyXG4gICAgICBjb25zb2xlLmxvZygn4pyFIFVzaW5nIHByaWNlLW1hdGNoZWQgc2xvdDonLCBzbG90LmlkLCBzbG90LmN1c3RvbV90aXRsZSwgYCgke3Nsb3Quc2xvdF9wcmljZX0pYCk7XHJcbiAgICAgIHJldHVybiBhd2FpdCBnZXRFdmVudEdhbWVTbG90RGV0YWlsc0J5U2xvdElkKHNsb3QuaWQpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIDIuIFRyeSB0byBtYXRjaCBieSBjdXN0b20gdGl0bGUgKHNsb3RzIHdpdGggY3VzdG9tIHRpdGxlcyBhcmUgbW9yZSBsaWtlbHkgdG8gYmUgdGhlIGJvb2tlZCBvbmVzKVxyXG4gICAgY29uc3QgY3VzdG9tU2xvdHMgPSBtYXRjaGluZ1Nsb3RzLmZpbHRlcigoc2xvdDogYW55KSA9PlxyXG4gICAgICBzbG90LmN1c3RvbV90aXRsZSAmJlxyXG4gICAgICBzbG90LmN1c3RvbV90aXRsZS50cmltKCkgIT09ICcnICYmXHJcbiAgICAgIHNsb3QuY3VzdG9tX3RpdGxlICE9PSBib29raW5nLmdhbWVfbmFtZVxyXG4gICAgKTtcclxuXHJcbiAgICBpZiAoY3VzdG9tU2xvdHMubGVuZ3RoID09PSAxKSB7XHJcbiAgICAgIGNvbnN0IHNsb3QgPSBjdXN0b21TbG90c1swXTtcclxuICAgICAgY29uc29sZS5sb2coJ+KchSBVc2luZyBjdXN0b20tdGl0bGVkIHNsb3Q6Jywgc2xvdC5pZCwgc2xvdC5jdXN0b21fdGl0bGUpO1xyXG4gICAgICByZXR1cm4gYXdhaXQgZ2V0RXZlbnRHYW1lU2xvdERldGFpbHNCeVNsb3RJZChzbG90LmlkKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyAzLiBUcnkgdG8gbWF0Y2ggYnkgY3JlYXRpb24gdGltZSAoc2xvdHMgY3JlYXRlZCBhcm91bmQgdGhlIHNhbWUgdGltZSBhcyBib29raW5nKVxyXG4gICAgaWYgKGJvb2tpbmcuYm9va2luZ19jcmVhdGVkX2F0KSB7XHJcbiAgICAgIGNvbnN0IGJvb2tpbmdUaW1lID0gbmV3IERhdGUoYm9va2luZy5ib29raW5nX2NyZWF0ZWRfYXQpO1xyXG4gICAgICBjb25zdCB0aW1lTWF0Y2hpbmdTbG90cyA9IG1hdGNoaW5nU2xvdHMuZmlsdGVyKChzbG90OiBhbnkpID0+IHtcclxuICAgICAgICBpZiAoIXNsb3QuY3JlYXRlZF9hdCkgcmV0dXJuIGZhbHNlO1xyXG4gICAgICAgIGNvbnN0IHNsb3RUaW1lID0gbmV3IERhdGUoc2xvdC5jcmVhdGVkX2F0KTtcclxuICAgICAgICBjb25zdCB0aW1lRGlmZiA9IE1hdGguYWJzKGJvb2tpbmdUaW1lLmdldFRpbWUoKSAtIHNsb3RUaW1lLmdldFRpbWUoKSk7XHJcbiAgICAgICAgLy8gV2l0aGluIDI0IGhvdXJzXHJcbiAgICAgICAgcmV0dXJuIHRpbWVEaWZmIDw9IDI0ICogNjAgKiA2MCAqIDEwMDA7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHRpbWVNYXRjaGluZ1Nsb3RzLmxlbmd0aCA9PT0gMSkge1xyXG4gICAgICAgIGNvbnN0IHNsb3QgPSB0aW1lTWF0Y2hpbmdTbG90c1swXTtcclxuICAgICAgICBjb25zb2xlLmxvZygn4pyFIFVzaW5nIHRpbWUtbWF0Y2hlZCBzbG90OicsIHNsb3QuaWQsIHNsb3QuY3VzdG9tX3RpdGxlKTtcclxuICAgICAgICByZXR1cm4gYXdhaXQgZ2V0RXZlbnRHYW1lU2xvdERldGFpbHNCeVNsb3RJZChzbG90LmlkKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIDQuIEZhbGxiYWNrOiBVc2UgdGhlIHNsb3Qgd2l0aCB0aGUgbW9zdCByZWNlbnQgY3JlYXRpb24gdGltZVxyXG4gICAgY29uc3Qgc29ydGVkU2xvdHMgPSBtYXRjaGluZ1Nsb3RzLnNvcnQoKGE6IGFueSwgYjogYW55KSA9PiB7XHJcbiAgICAgIGNvbnN0IHRpbWVBID0gbmV3IERhdGUoYS5jcmVhdGVkX2F0IHx8IDApLmdldFRpbWUoKTtcclxuICAgICAgY29uc3QgdGltZUIgPSBuZXcgRGF0ZShiLmNyZWF0ZWRfYXQgfHwgMCkuZ2V0VGltZSgpO1xyXG4gICAgICByZXR1cm4gdGltZUIgLSB0aW1lQTsgLy8gTW9zdCByZWNlbnQgZmlyc3RcclxuICAgIH0pO1xyXG5cclxuICAgIGNvbnN0IHNsb3QgPSBzb3J0ZWRTbG90c1swXTtcclxuICAgIGNvbnNvbGUubG9nKCfinIUgVXNpbmcgbW9zdCByZWNlbnQgc2xvdCBhcyBmYWxsYmFjazonLCBzbG90LmlkLCBzbG90LmN1c3RvbV90aXRsZSk7XHJcbiAgICByZXR1cm4gYXdhaXQgZ2V0RXZlbnRHYW1lU2xvdERldGFpbHNCeVNsb3RJZChzbG90LmlkKTtcclxuXHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBmaW5kaW5nIG1vc3QgbGlrZWx5IHNsb3Q6JywgZXJyb3IpO1xyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogR2V0IGV2ZW50IGdhbWUgc2xvdCBkZXRhaWxzIGJ5IGV2ZW50IGFuZCBnYW1lIElEcyAoZmFsbGJhY2sgbWV0aG9kKVxyXG4gKiBAcGFyYW0gZXZlbnRJZCBFdmVudCBJRFxyXG4gKiBAcGFyYW0gZ2FtZUlkIEdhbWUgSURcclxuICogQHJldHVybnMgUHJvbWlzZSB3aXRoIHNsb3QgZGV0YWlscyBvciBudWxsXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0RXZlbnRHYW1lU2xvdERldGFpbHMoZXZlbnRJZDogbnVtYmVyLCBnYW1lSWQ6IG51bWJlcikge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCdodHRwczovL2FpLmFsdmlvbmdzLmNvbS93ZWJob29rL3YxL25pYm9nL2V2ZW50LWdhbWUtc2xvdC9nZXQtYWxsJywge1xyXG4gICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcclxuICAgIH0pO1xyXG5cclxuICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zdCBhbGxTbG90cyA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgY29uc3QgbWF0Y2hpbmdTbG90ID0gYWxsU2xvdHMuZmluZCgoc2xvdDogYW55KSA9PlxyXG4gICAgICAgIHNsb3QuZXZlbnRfaWQgPT09IGV2ZW50SWQgJiYgc2xvdC5nYW1lX2lkID09PSBnYW1lSWRcclxuICAgICAgKTtcclxuXHJcbiAgICAgIGlmIChtYXRjaGluZ1Nsb3QpIHtcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgc2xvdF9pZDogbWF0Y2hpbmdTbG90LmlkLFxyXG4gICAgICAgICAgY3VzdG9tX3RpdGxlOiBtYXRjaGluZ1Nsb3QuY3VzdG9tX3RpdGxlLFxyXG4gICAgICAgICAgY3VzdG9tX2Rlc2NyaXB0aW9uOiBtYXRjaGluZ1Nsb3QuY3VzdG9tX2Rlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgY3VzdG9tX3ByaWNlOiBtYXRjaGluZ1Nsb3QuY3VzdG9tX3ByaWNlLFxyXG4gICAgICAgICAgc2xvdF9wcmljZTogbWF0Y2hpbmdTbG90LnNsb3RfcHJpY2UsXHJcbiAgICAgICAgICBzdGFydF90aW1lOiBtYXRjaGluZ1Nsb3Quc3RhcnRfdGltZSxcclxuICAgICAgICAgIGVuZF90aW1lOiBtYXRjaGluZ1Nsb3QuZW5kX3RpbWUsXHJcbiAgICAgICAgICBtYXhfcGFydGljaXBhbnRzOiBtYXRjaGluZ1Nsb3QubWF4X3BhcnRpY2lwYW50cyxcclxuICAgICAgICB9O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHNsb3QgZGV0YWlsczonLCBlcnJvcik7XHJcbiAgfVxyXG4gIHJldHVybiBudWxsO1xyXG59XHJcblxyXG4vKipcclxuICogR2V0IHBheW1lbnQgZGV0YWlscyBmb3IgYSBib29raW5nXHJcbiAqIEBwYXJhbSBib29raW5nSWQgQm9va2luZyBJRFxyXG4gKiBAcmV0dXJucyBQcm9taXNlIHdpdGggcGF5bWVudCBkZXRhaWxzIG9yIG51bGxcclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRCb29raW5nUGF5bWVudERldGFpbHMoYm9va2luZ0lkOiBudW1iZXIpIHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnaHR0cHM6Ly9haS5hbHZpb25ncy5jb20vd2ViaG9vay92MS9uaWJvZy9wYXltZW50cy9nZXQtYWxsJywge1xyXG4gICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcclxuICAgIH0pO1xyXG5cclxuICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zdCBhbGxQYXltZW50cyA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgY29uc3QgcGF5bWVudCA9IGFsbFBheW1lbnRzLmZpbmQoKHA6IGFueSkgPT4gcC5ib29raW5nX2lkID09PSBib29raW5nSWQpO1xyXG5cclxuICAgICAgaWYgKHBheW1lbnQpIHtcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgcGF5bWVudF9pZDogcGF5bWVudC5wYXltZW50X2lkLFxyXG4gICAgICAgICAgYWN0dWFsX3BheW1lbnRfc3RhdHVzOiBwYXltZW50LnBheW1lbnRfc3RhdHVzLFxyXG4gICAgICAgICAgdHJhbnNhY3Rpb25faWQ6IHBheW1lbnQudHJhbnNhY3Rpb25faWQsXHJcbiAgICAgICAgICBwYXltZW50X2RhdGU6IHBheW1lbnQucGF5bWVudF9kYXRlLFxyXG4gICAgICAgICAgcGF5bWVudF9tZXRob2Q6IHBheW1lbnQucGF5bWVudF9tZXRob2QsXHJcbiAgICAgICAgfTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwYXltZW50IGRldGFpbHM6JywgZXJyb3IpO1xyXG4gIH1cclxuICByZXR1cm4gbnVsbDtcclxufVxyXG5cclxuLyoqXHJcbiAqIFVwZGF0ZSBib29raW5nIHBheW1lbnQgc3RhdHVzXHJcbiAqIEBwYXJhbSBib29raW5nSWQgQm9va2luZyBJRFxyXG4gKiBAcGFyYW0gcGF5bWVudFN0YXR1cyBOZXcgcGF5bWVudCBzdGF0dXNcclxuICogQHJldHVybnMgUHJvbWlzZSB3aXRoIHVwZGF0ZWQgYm9va2luZyBkYXRhXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdXBkYXRlQm9va2luZ1BheW1lbnRTdGF0dXMoYm9va2luZ0lkOiBudW1iZXIsIHBheW1lbnRTdGF0dXM6IHN0cmluZyk6IFByb21pc2U8YW55PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnNvbGUubG9nKGBVcGRhdGluZyBib29raW5nICR7Ym9va2luZ0lkfSBwYXltZW50IHN0YXR1cyB0bzogJHtwYXltZW50U3RhdHVzfWApO1xyXG5cclxuICAgIC8vIFVzZSBvdXIgaW50ZXJuYWwgQVBJIHJvdXRlIHRoYXQgaGFuZGxlcyBib3RoIHBheW1lbnQgc3RhdHVzIGFuZCBib29raW5nIHN0YXR1cyB1cGRhdGVzXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2Jvb2tpbmdzL3VwZGF0ZS1wYXltZW50LXN0YXR1cycsIHtcclxuICAgICAgbWV0aG9kOiBcIlBPU1RcIixcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICB9LFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgYm9va2luZ0lkLFxyXG4gICAgICAgIHBheW1lbnRTdGF0dXNcclxuICAgICAgfSksXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gdXBkYXRlIGJvb2tpbmcgcGF5bWVudCBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzfSAtICR7ZXJyb3JUZXh0fWApO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgYm9va2luZyBwYXltZW50IHN0YXR1czonLCBlcnJvcik7XHJcbiAgICB0aHJvdyBlcnJvcjtcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBDcmVhdGUgYSBuZXcgYm9va2luZ1xyXG4gKiBAcGFyYW0gYm9va2luZ0RhdGEgVGhlIGJvb2tpbmcgZGF0YSB0byBjcmVhdGVcclxuICogQHJldHVybnMgUHJvbWlzZSB3aXRoIHRoZSBjcmVhdGVkIGJvb2tpbmcgZGF0YVxyXG4gKi9cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUJvb2tpbmcoYm9va2luZ0RhdGE6IHtcclxuICBwYXJlbnQ6IHtcclxuICAgIHVzZXJfaWQ6IG51bWJlcjtcclxuICAgIHBhcmVudF9uYW1lOiBzdHJpbmc7XHJcbiAgICBlbWFpbDogc3RyaW5nO1xyXG4gICAgYWRkaXRpb25hbF9waG9uZTogc3RyaW5nO1xyXG4gIH07XHJcbiAgY2hpbGRyZW46IHtcclxuICAgIGZ1bGxfbmFtZTogc3RyaW5nO1xyXG4gICAgZGF0ZV9vZl9iaXJ0aDogc3RyaW5nO1xyXG4gICAgc2Nob29sX25hbWU6IHN0cmluZztcclxuICAgIGdlbmRlcjogc3RyaW5nO1xyXG4gIH07XHJcbiAgYm9va2luZzoge1xyXG4gICAgdXNlcl9pZDogbnVtYmVyO1xyXG4gICAgZXZlbnRfaWQ6IG51bWJlcjtcclxuICAgIHRvdGFsX2Ftb3VudDogbnVtYmVyO1xyXG4gICAgcGF5bWVudF9tZXRob2Q6IHN0cmluZztcclxuICAgIHBheW1lbnRfc3RhdHVzOiBzdHJpbmc7XHJcbiAgICB0ZXJtc19hY2NlcHRlZDogYm9vbGVhbjtcclxuICB9O1xyXG4gIGJvb2tpbmdfZ2FtZXM6IHtcclxuICAgIGdhbWVfaWQ6IG51bWJlcjtcclxuICAgIGNoaWxkX2luZGV4OiBudW1iZXI7XHJcbiAgICBnYW1lX3ByaWNlOiBudW1iZXI7XHJcbiAgfTtcclxufSk6IFByb21pc2U8YW55PiB7XHJcbiAgdHJ5IHtcclxuICAgIC8vIFVzZSBvdXIgaW50ZXJuYWwgQVBJIHJvdXRlIHRvIGF2b2lkIENPUlMgaXNzdWVzXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2Jvb2tpbmdzL2NyZWF0ZScsIHtcclxuICAgICAgbWV0aG9kOiBcIlBPU1RcIixcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICB9LFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShib29raW5nRGF0YSksXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihgQVBJIHJldHVybmVkIGVycm9yIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YCk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgIHJldHVybiBkYXRhO1xyXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuXHJcbiJdLCJuYW1lcyI6WyJnZXRBbGxCb29raW5ncyIsImNvbnRyb2xsZXIiLCJBYm9ydENvbnRyb2xsZXIiLCJ0aW1lb3V0SWQiLCJzZXRUaW1lb3V0IiwiYWJvcnQiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsInNpZ25hbCIsImNsZWFyVGltZW91dCIsIm9rIiwiRXJyb3IiLCJzdGF0dXMiLCJkYXRhIiwianNvbiIsIkFycmF5IiwiaXNBcnJheSIsImZldGNoRXJyb3IiLCJuYW1lIiwiZXJyb3IiLCJnZXRQYWdpbmF0ZWRCb29raW5ncyIsInBhcmFtcyIsInBhZ2UiLCJsaW1pdCIsInVybCIsIlVSTCIsIndpbmRvdyIsImxvY2F0aW9uIiwib3JpZ2luIiwic2VhcmNoUGFyYW1zIiwic2V0IiwidG9TdHJpbmciLCJwYWdpbmF0aW9uIiwibGVuZ3RoIiwidG90YWwiLCJ0b3RhbFBhZ2VzIiwiaGFzTmV4dCIsImhhc1ByZXYiLCJ1cGRhdGVCb29raW5nU3RhdHVzIiwiYm9va2luZ0lkIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJlcnJvclRleHQiLCJ0ZXh0IiwiZXJyb3JNZXNzYWdlIiwiZXJyb3JEYXRhIiwicGFyc2UiLCJlIiwiZ2V0Qm9va2luZ0J5SWQiLCJnZXRFdmVudFdpdGhWZW51ZURldGFpbHMiLCJldmVudElkIiwiY29uc29sZSIsImxvZyIsImV2ZW50RGF0YSIsImNvbnZlcnRCb29raW5nUmVmRm9ybWF0IiwicmVmIiwidGFyZ2V0Rm9ybWF0IiwiY2xlYW5SZWYiLCJzdGFydHNXaXRoIiwiZW5kc1dpdGgiLCJzbGljZSIsIm51bWVyaWNQYXJ0IiwiY3VycmVudERhdGUiLCJEYXRlIiwieWVhciIsImdldEZ1bGxZZWFyIiwibW9udGgiLCJnZXRNb250aCIsInBhZFN0YXJ0IiwiZGF5IiwiZ2V0RGF0ZSIsInJlcGxhY2UiLCJzcGxpdCIsIm1hcCIsImMiLCJjaGFyQ29kZUF0IiwicHB0TWF0Y2giLCJtYXRjaCIsImRhdGVTdHIiLCJpZFBhcnQiLCJhbHRNYXRjaCIsImZ1bGxOdW1iZXIiLCJzdWJzdHJpbmciLCJmYWxsYmFja1Jlc3VsdCIsImdldFRpY2tldERldGFpbHMiLCJib29raW5nUmVmIiwiZm9ybWF0dGVkUmVmIiwiYm9va2luZ19yZWZfaWQiLCJzdGF0dXNUZXh0IiwiZ2V0RXZlbnRHYW1lU2xvdERldGFpbHNCeVNsb3RJZCIsInNsb3RJZCIsImlkIiwic2xvdERhdGFBcnJheSIsInNsb3REYXRhIiwic2xvdF9pZCIsImN1c3RvbV90aXRsZSIsImN1c3RvbV9kZXNjcmlwdGlvbiIsImN1c3RvbV9wcmljZSIsInNsb3RfcHJpY2UiLCJzdGFydF90aW1lIiwiZW5kX3RpbWUiLCJtYXhfcGFydGljaXBhbnRzIiwiZmluZE1vc3RMaWtlbHlTbG90Rm9yQm9va2luZyIsImJvb2tpbmciLCJib29raW5nX2lkIiwiZXZlbnRfdGl0bGUiLCJldmVudF9kYXRlIiwiZXZlbnRfZXZlbnRfZGF0ZSIsImdhbWVfbmFtZSIsInRvdGFsX2Ftb3VudCIsImJvb2tpbmdfY3JlYXRlZF9hdCIsInNsb3RzQ29udHJvbGxlciIsInNsb3RzVGltZW91dCIsInNsb3RzUmVzcG9uc2UiLCJhbGxTbG90cyIsImV2ZW50c0NvbnRyb2xsZXIiLCJldmVudHNUaW1lb3V0IiwiZXZlbnRzUmVzcG9uc2UiLCJhbGxFdmVudHMiLCJtYXRjaGluZ0V2ZW50IiwiZmluZCIsImV2ZW50IiwiZXZlbnREYXRlIiwidG9EYXRlU3RyaW5nIiwiYm9va2luZ0RhdGUiLCJ0aXRsZSIsImdhbWVzQ29udHJvbGxlciIsImdhbWVzVGltZW91dCIsImdhbWVzUmVzcG9uc2UiLCJhbGxHYW1lcyIsIm1hdGNoaW5nR2FtZSIsImdhbWUiLCJnYW1lX3RpdGxlIiwibWF0Y2hpbmdTbG90cyIsImZpbHRlciIsInNsb3QiLCJldmVudF9pZCIsImdhbWVfaWQiLCJmb3JFYWNoIiwiaW5kZXgiLCJib29raW5nQW1vdW50IiwicGFyc2VGbG9hdCIsInByaWNlTWF0Y2hpbmdTbG90cyIsInNsb3RQcmljZSIsIk1hdGgiLCJhYnMiLCJjdXN0b21TbG90cyIsInRyaW0iLCJib29raW5nVGltZSIsInRpbWVNYXRjaGluZ1Nsb3RzIiwiY3JlYXRlZF9hdCIsInNsb3RUaW1lIiwidGltZURpZmYiLCJnZXRUaW1lIiwic29ydGVkU2xvdHMiLCJzb3J0IiwiYSIsImIiLCJ0aW1lQSIsInRpbWVCIiwiZ2V0RXZlbnRHYW1lU2xvdERldGFpbHMiLCJnYW1lSWQiLCJtYXRjaGluZ1Nsb3QiLCJnZXRCb29raW5nUGF5bWVudERldGFpbHMiLCJhbGxQYXltZW50cyIsInBheW1lbnQiLCJwIiwicGF5bWVudF9pZCIsImFjdHVhbF9wYXltZW50X3N0YXR1cyIsInBheW1lbnRfc3RhdHVzIiwidHJhbnNhY3Rpb25faWQiLCJwYXltZW50X2RhdGUiLCJwYXltZW50X21ldGhvZCIsInVwZGF0ZUJvb2tpbmdQYXltZW50U3RhdHVzIiwicGF5bWVudFN0YXR1cyIsImNyZWF0ZUJvb2tpbmciLCJib29raW5nRGF0YSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./services/bookingService.ts\n");

/***/ })

};
;