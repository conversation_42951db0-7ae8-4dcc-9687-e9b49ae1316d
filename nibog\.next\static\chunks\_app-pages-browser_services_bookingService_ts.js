"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_services_bookingService_ts"],{

/***/ "(app-pages-browser)/./services/bookingService.ts":
/*!************************************!*\
  !*** ./services/bookingService.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertBookingRefFormat: () => (/* binding */ convertBookingRefFormat),\n/* harmony export */   createBooking: () => (/* binding */ createBooking),\n/* harmony export */   findMostLikelySlotForBooking: () => (/* binding */ findMostLikelySlotForBooking),\n/* harmony export */   getAllBookings: () => (/* binding */ getAllBookings),\n/* harmony export */   getBookingById: () => (/* binding */ getBookingById),\n/* harmony export */   getBookingPaymentDetails: () => (/* binding */ getBookingPaymentDetails),\n/* harmony export */   getEventGameSlotDetails: () => (/* binding */ getEventGameSlotDetails),\n/* harmony export */   getEventGameSlotDetailsBySlotId: () => (/* binding */ getEventGameSlotDetailsBySlotId),\n/* harmony export */   getEventWithVenueDetails: () => (/* binding */ getEventWithVenueDetails),\n/* harmony export */   getPaginatedBookings: () => (/* binding */ getPaginatedBookings),\n/* harmony export */   getTicketDetails: () => (/* binding */ getTicketDetails),\n/* harmony export */   updateBookingPaymentStatus: () => (/* binding */ updateBookingPaymentStatus),\n/* harmony export */   updateBookingStatus: () => (/* binding */ updateBookingStatus)\n/* harmony export */ });\n// Booking service for handling booking-related API calls\n/**\r\n * Get all bookings with error handling and timeout\r\n * @returns Promise with array of bookings\r\n */ async function getAllBookings() {\n    try {\n        // Use our internal API route to avoid CORS issues\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000); // 30 second timeout\n        try {\n            const response = await fetch('/api/bookings/get-all', {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                throw new Error(\"API returned error status: \".concat(response.status));\n            }\n            const data = await response.json();\n            // Handle both old format (array) and new paginated format\n            if (Array.isArray(data)) {\n                return data;\n            } else if (data.data && Array.isArray(data.data)) {\n                return data.data;\n            }\n            return [];\n        } catch (fetchError) {\n            clearTimeout(timeoutId);\n            if (fetchError.name === 'AbortError') {\n                throw new Error(\"Request timed out. The server took too long to respond.\");\n            }\n            throw fetchError;\n        }\n    } catch (error) {\n        throw error;\n    }\n}\n/**\r\n * Get paginated bookings with error handling and timeout\r\n * @param params Pagination parameters\r\n * @returns Promise with paginated bookings response\r\n */ async function getPaginatedBookings() {\n    let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    try {\n        const { page = 1, limit = 100 } = params;\n        // Use our internal API route to avoid CORS issues\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000); // 30 second timeout\n        try {\n            const url = new URL('/api/bookings/get-all', window.location.origin);\n            url.searchParams.set('page', page.toString());\n            url.searchParams.set('limit', limit.toString());\n            const response = await fetch(url.toString(), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                throw new Error(\"API returned error status: \".concat(response.status));\n            }\n            const data = await response.json();\n            // Ensure we have the expected paginated format\n            if (data.data && data.pagination) {\n                return data;\n            }\n            // Fallback for old format\n            if (Array.isArray(data)) {\n                return {\n                    data,\n                    pagination: {\n                        page: 1,\n                        limit: data.length,\n                        total: data.length,\n                        totalPages: 1,\n                        hasNext: false,\n                        hasPrev: false\n                    }\n                };\n            }\n            throw new Error(\"Invalid response format\");\n        } catch (fetchError) {\n            clearTimeout(timeoutId);\n            if (fetchError.name === 'AbortError') {\n                throw new Error(\"Request timed out. The server took too long to respond.\");\n            }\n            throw fetchError;\n        }\n    } catch (error) {\n        throw error;\n    }\n}\n/**\r\n * Update booking status\r\n * @param bookingId Booking ID\r\n * @param status New status\r\n * @returns Promise with updated booking data\r\n */ async function updateBookingStatus(bookingId, status) {\n    try {\n        // Use our internal API route to avoid CORS issues\n        const response = await fetch('/api/bookings/update-status', {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                bookingId,\n                status\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            let errorMessage = \"API returned error status: \".concat(response.status);\n            try {\n                const errorData = JSON.parse(errorText);\n                if (errorData.error) {\n                    errorMessage = errorData.error;\n                }\n            } catch (e) {\n            // If we can't parse the error as JSON, use the status code\n            }\n            throw new Error(errorMessage);\n        }\n        const data = await response.json();\n        // The API returns an array with a single booking object\n        if (Array.isArray(data) && data.length > 0) {\n            return data[0];\n        } else if (!Array.isArray(data)) {\n            return data;\n        }\n        throw new Error(\"Invalid response format from status update API\");\n    } catch (error) {\n        throw error;\n    }\n}\n/**\r\n * Get booking by ID\r\n * @param bookingId Booking ID\r\n * @returns Promise with booking data\r\n */ async function getBookingById(bookingId) {\n    try {\n        // Create an AbortController for timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 15000); // 15 second timeout\n        // Use our internal API route to avoid CORS issues\n        const response = await fetch(\"/api/bookings/get/\".concat(bookingId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            // If it's a 404, the booking doesn't exist\n            if (response.status === 404) {\n                throw new Error(\"Booking with ID \".concat(bookingId, \" not found\"));\n            }\n            // For other errors, throw immediately without fallback to prevent loops\n            throw new Error(\"Failed to fetch booking: \".concat(response.status));\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        // Handle timeout errors\n        if (error.name === 'AbortError') {\n            throw new Error('Request timeout - the booking service is taking too long to respond');\n        }\n        // Re-throw the error without fallback to prevent infinite loops\n        throw error;\n    }\n}\n/**\r\n * Get event details with venue information by event ID\r\n * @param eventId Event ID\r\n * @returns Promise with event details including venue information\r\n */ async function getEventWithVenueDetails(eventId) {\n    try {\n        console.log('Fetching event details with venue information for event ID:', eventId);\n        const response = await fetch(\"/api/events/get-with-games\", {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                eventId\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch event details: \".concat(response.status));\n        }\n        const eventData = await response.json();\n        console.log('Event details with venue information:', eventData);\n        return eventData;\n    } catch (error) {\n        console.error('Error fetching event details with venue information:', error);\n        throw error;\n    }\n}\n/**\r\n * Converts booking references between formats\r\n * @param ref The booking reference to convert\r\n * @param targetFormat The target format for the conversion ('B' or 'PPT')\r\n * @returns The converted booking reference\r\n */ function convertBookingRefFormat(ref) {\n    let targetFormat = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'PPT';\n    if (!ref) return '';\n    // Remove quotes if it's a JSON string\n    let cleanRef = ref;\n    if (cleanRef.startsWith('\"') && cleanRef.endsWith('\"')) {\n        cleanRef = cleanRef.slice(1, -1);\n    }\n    // Extract numeric parts based on current format\n    let numericPart = '';\n    const currentDate = new Date();\n    const year = currentDate.getFullYear().toString().slice(-2);\n    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');\n    const day = currentDate.getDate().toString().padStart(2, '0');\n    if (cleanRef.startsWith('B')) {\n        // Extract from B format (B0000123)\n        numericPart = cleanRef.replace(/^B(\\d+)$/, '$1');\n        if (targetFormat === 'B') {\n            // Already in B format, just normalize\n            return \"B\".concat(numericPart.padStart(7, '0'));\n        } else {\n            // Convert B -> PPT format\n            // Use current date + numeric part as the identifier\n            return \"PPT\".concat(year).concat(month).concat(day).concat(numericPart.slice(-3).padStart(3, '0'));\n        }\n    } else if (cleanRef.startsWith('PPT')) {\n        // Extract from PPT format (PPTYYMMDDxxx)\n        // PPT format typically has date embedded in it\n        console.log(\"Converting PPT reference: \".concat(cleanRef));\n        console.log(\"PPT reference length: \".concat(cleanRef.length));\n        console.log(\"PPT reference characters:\", cleanRef.split('').map((c)=>\"\".concat(c, \"(\").concat(c.charCodeAt(0), \")\")));\n        // More flexible regex that handles various PPT formats\n        const pptMatch = cleanRef.match(/^PPT(\\d{6})(\\d+)$/);\n        console.log(\"PPT regex match result:\", pptMatch);\n        if (pptMatch) {\n            const dateStr = pptMatch[1]; // YYMMDD part\n            const idPart = pptMatch[2]; // xxx part (numeric ID)\n            console.log(\"PPT matched - dateStr: \".concat(dateStr, \", idPart: \").concat(idPart));\n            if (targetFormat === 'PPT') {\n                // Already in PPT format, return as-is to preserve original date\n                console.log(\"Returning PPT as-is: \".concat(cleanRef));\n                return cleanRef;\n            } else {\n                // Convert PPT -> B format\n                // Use the numeric part as is, pad to 7 digits\n                return \"B\".concat(idPart.padStart(7, '0'));\n            }\n        } else {\n            // Try alternative patterns for PPT references\n            console.log(\"Primary PPT regex failed, trying alternative patterns...\");\n            // Check if it's a valid PPT format with any number of digits after the date\n            const altMatch = cleanRef.match(/^PPT(\\d+)$/);\n            if (altMatch && altMatch[1].length >= 9) {\n                const fullNumber = altMatch[1];\n                const dateStr = fullNumber.substring(0, 6); // First 6 digits as date\n                const idPart = fullNumber.substring(6); // Rest as ID\n                console.log(\"Alternative PPT pattern matched - dateStr: \".concat(dateStr, \", idPart: \").concat(idPart));\n                if (targetFormat === 'PPT') {\n                    // Already in PPT format, return as-is to preserve original date\n                    console.log(\"Returning PPT as-is: \".concat(cleanRef));\n                    return cleanRef;\n                } else {\n                    // Convert PPT -> B format\n                    return \"B\".concat(idPart.padStart(7, '0'));\n                }\n            }\n            // Last resort - if target format is PPT and input is already PPT, preserve it\n            if (targetFormat === 'PPT') {\n                console.log(\"PPT reference considered malformed but preserving original: \".concat(cleanRef));\n                return cleanRef; // Preserve original PPT reference to avoid date changes\n            } else {\n                // Only convert to B format if explicitly requested\n                console.log(\"PPT reference considered malformed, converting to B format\");\n                numericPart = cleanRef.replace(/\\D/g, '');\n                const fallbackResult = \"B\".concat(numericPart.slice(-7).padStart(7, '0'));\n                console.log(\"Fallback result: \".concat(fallbackResult));\n                return fallbackResult;\n            }\n        }\n    } else {\n        // Unknown format, extract any numeric parts\n        numericPart = cleanRef.replace(/\\D/g, '');\n        return targetFormat === 'B' ? \"B\".concat(numericPart.slice(-7).padStart(7, '0')) : \"PPT\".concat(year).concat(month).concat(day).concat(numericPart.slice(-3).padStart(3, '0'));\n    }\n}\n// New function to fetch detailed ticket information using booking reference\nasync function getTicketDetails(bookingRef) {\n    try {\n        console.log('Fetching ticket details with booking reference:', bookingRef);\n        // API expects booking_ref_id in PPT format for this specific endpoint\n        // If already in PPT format, use as-is. Otherwise convert from B format.\n        let formattedRef = bookingRef;\n        if (!bookingRef.startsWith('PPT')) {\n            formattedRef = convertBookingRefFormat(bookingRef, 'PPT');\n            console.log(\"Converted booking reference to API format: \".concat(bookingRef, \" -> \").concat(formattedRef));\n        } else {\n            console.log(\"Booking reference already in PPT format: \".concat(bookingRef));\n        }\n        // Strip any JSON formatting if it was stored as JSON string\n        if (formattedRef.startsWith('\"') && formattedRef.endsWith('\"')) {\n            formattedRef = formattedRef.slice(1, -1);\n            console.log('Stripped JSON quotes from booking reference:', formattedRef);\n        }\n        console.log('Making API call with formatted booking reference:', formattedRef);\n        const response = await fetch('https://ai.alviongs.com/webhook/v1/nibog/tickect/booking_ref/details', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                booking_ref_id: formattedRef // Using exact parameter name expected by the API\n            })\n        });\n        if (!response.ok) {\n            console.error(\"API returned error status: \".concat(response.status, \" \").concat(response.statusText));\n            const errorText = await response.text();\n            console.error('Error response body:', errorText);\n            throw new Error(\"Failed to fetch ticket details: \".concat(response.status));\n        }\n        const data = await response.json();\n        console.log('Received ticket details:', data);\n        return data;\n    } catch (error) {\n        console.error('Error fetching ticket details:', error);\n        throw error;\n    }\n}\n/**\r\n * Get event game slot details by slot ID (preferred method)\r\n * @param slotId Slot ID\r\n * @returns Promise with slot details or null\r\n */ async function getEventGameSlotDetailsBySlotId(slotId) {\n    try {\n        const response = await fetch('https://ai.alviongs.com/webhook/v1/nibog/event-game-slot/get', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                id: slotId\n            })\n        });\n        if (response.ok) {\n            const slotDataArray = await response.json();\n            // The API returns an array, get the first item\n            if (slotDataArray && Array.isArray(slotDataArray) && slotDataArray.length > 0) {\n                const slotData = slotDataArray[0];\n                return {\n                    slot_id: slotData.id,\n                    custom_title: slotData.custom_title,\n                    custom_description: slotData.custom_description,\n                    custom_price: slotData.custom_price,\n                    slot_price: slotData.slot_price,\n                    start_time: slotData.start_time,\n                    end_time: slotData.end_time,\n                    max_participants: slotData.max_participants\n                };\n            }\n        }\n    } catch (error) {\n        console.error('Error fetching slot details by slot ID:', error);\n    }\n    return null;\n}\n/**\r\n * Find the most likely slot for a booking based on event and game details\r\n * This is a workaround for when booking_games data is not available\r\n * @param booking Booking data\r\n * @returns Promise with slot details or null\r\n */ async function findMostLikelySlotForBooking(booking) {\n    try {\n        console.log('🔍 Finding most likely slot for booking:', booking.booking_id);\n        console.log('📋 Booking details:', {\n            event_title: booking.event_title,\n            event_date: booking.event_event_date,\n            game_name: booking.game_name,\n            total_amount: booking.total_amount,\n            booking_created_at: booking.booking_created_at\n        });\n        // Get all slots first with timeout protection\n        const slotsController = new AbortController();\n        const slotsTimeout = setTimeout(()=>slotsController.abort(), 10000); // 10 second timeout\n        const slotsResponse = await fetch('https://ai.alviongs.com/webhook/v1/nibog/event-game-slot/get-all', {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            signal: slotsController.signal\n        });\n        clearTimeout(slotsTimeout);\n        if (!slotsResponse.ok) {\n            console.error('❌ Failed to fetch slots:', slotsResponse.status);\n            return null;\n        }\n        const allSlots = await slotsResponse.json();\n        console.log(\"\\uD83D\\uDCCA Total slots available: \".concat(allSlots.length));\n        // Get all events to find the matching event ID with timeout protection\n        const eventsController = new AbortController();\n        const eventsTimeout = setTimeout(()=>eventsController.abort(), 10000); // 10 second timeout\n        const eventsResponse = await fetch('https://ai.alviongs.com/webhook/v1/nibog/event/get-all', {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            signal: eventsController.signal\n        });\n        clearTimeout(eventsTimeout);\n        if (!eventsResponse.ok) {\n            console.error('❌ Failed to fetch events:', eventsResponse.status);\n            return null;\n        }\n        const allEvents = await eventsResponse.json();\n        // Find the event that matches the booking\n        const matchingEvent = allEvents.find((event)=>{\n            const eventDate = new Date(event.event_date).toDateString();\n            const bookingDate = new Date(booking.event_event_date).toDateString();\n            return event.title === booking.event_title && eventDate === bookingDate;\n        });\n        if (!matchingEvent) {\n            console.log('❌ No matching event found for:', booking.event_title);\n            return null;\n        }\n        console.log('✅ Found matching event:', matchingEvent.id, matchingEvent.title);\n        // Get all games to find the matching game ID with timeout protection\n        const gamesController = new AbortController();\n        const gamesTimeout = setTimeout(()=>gamesController.abort(), 10000); // 10 second timeout\n        const gamesResponse = await fetch('https://ai.alviongs.com/webhook/v1/nibog/baby-games/get-all', {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            signal: gamesController.signal\n        });\n        clearTimeout(gamesTimeout);\n        if (!gamesResponse.ok) {\n            console.error('❌ Failed to fetch games:', gamesResponse.status);\n            return null;\n        }\n        const allGames = await gamesResponse.json();\n        // Find the game that matches the booking\n        const matchingGame = allGames.find((game)=>game.game_title === booking.game_name || game.name === booking.game_name || game.title === booking.game_name);\n        if (!matchingGame) {\n            console.log('❌ No matching game found for:', booking.game_name);\n            return null;\n        }\n        console.log('✅ Found matching game:', matchingGame.id, matchingGame.game_title || matchingGame.name);\n        // Find slots that match both event and game\n        const matchingSlots = allSlots.filter((slot)=>slot.event_id === matchingEvent.id && slot.game_id === matchingGame.id);\n        console.log(\"\\uD83C\\uDFAF Found \".concat(matchingSlots.length, \" matching slots for event \").concat(matchingEvent.id, \" + game \").concat(matchingGame.id));\n        if (matchingSlots.length === 0) {\n            console.log('❌ No matching slots found');\n            return null;\n        }\n        // Log all matching slots for debugging\n        matchingSlots.forEach((slot, index)=>{\n            console.log(\"  \".concat(index + 1, \". Slot \").concat(slot.id, ': \"').concat(slot.custom_title || 'No custom title', '\" - Price: ').concat(slot.slot_price));\n        });\n        // If there's only one slot, use it\n        if (matchingSlots.length === 1) {\n            const slot = matchingSlots[0];\n            console.log('✅ Using single matching slot:', slot.id, slot.custom_title);\n            return await getEventGameSlotDetailsBySlotId(slot.id);\n        }\n        // IMPROVED MATCHING LOGIC for multiple slots:\n        // 1. Try to match by price (booking total_amount should match slot_price + any addons)\n        const bookingAmount = parseFloat(booking.total_amount);\n        const priceMatchingSlots = matchingSlots.filter((slot)=>{\n            const slotPrice = parseFloat(slot.slot_price || slot.custom_price || '0');\n            // Allow for small differences due to addons, taxes, etc.\n            return Math.abs(bookingAmount - slotPrice) <= 2; // Within $2 difference\n        });\n        if (priceMatchingSlots.length === 1) {\n            const slot = priceMatchingSlots[0];\n            console.log('✅ Using price-matched slot:', slot.id, slot.custom_title, \"(\".concat(slot.slot_price, \")\"));\n            return await getEventGameSlotDetailsBySlotId(slot.id);\n        }\n        // 2. Try to match by custom title (slots with custom titles are more likely to be the booked ones)\n        const customSlots = matchingSlots.filter((slot)=>slot.custom_title && slot.custom_title.trim() !== '' && slot.custom_title !== booking.game_name);\n        if (customSlots.length === 1) {\n            const slot = customSlots[0];\n            console.log('✅ Using custom-titled slot:', slot.id, slot.custom_title);\n            return await getEventGameSlotDetailsBySlotId(slot.id);\n        }\n        // 3. Try to match by creation time (slots created around the same time as booking)\n        if (booking.booking_created_at) {\n            const bookingTime = new Date(booking.booking_created_at);\n            const timeMatchingSlots = matchingSlots.filter((slot)=>{\n                if (!slot.created_at) return false;\n                const slotTime = new Date(slot.created_at);\n                const timeDiff = Math.abs(bookingTime.getTime() - slotTime.getTime());\n                // Within 24 hours\n                return timeDiff <= 24 * 60 * 60 * 1000;\n            });\n            if (timeMatchingSlots.length === 1) {\n                const slot = timeMatchingSlots[0];\n                console.log('✅ Using time-matched slot:', slot.id, slot.custom_title);\n                return await getEventGameSlotDetailsBySlotId(slot.id);\n            }\n        }\n        // 4. Fallback: Use the slot with the most recent creation time\n        const sortedSlots = matchingSlots.sort((a, b)=>{\n            const timeA = new Date(a.created_at || 0).getTime();\n            const timeB = new Date(b.created_at || 0).getTime();\n            return timeB - timeA; // Most recent first\n        });\n        const slot = sortedSlots[0];\n        console.log('✅ Using most recent slot as fallback:', slot.id, slot.custom_title);\n        return await getEventGameSlotDetailsBySlotId(slot.id);\n    } catch (error) {\n        console.error('❌ Error finding most likely slot:', error);\n        return null;\n    }\n}\n/**\r\n * Get event game slot details by event and game IDs (fallback method)\r\n * @param eventId Event ID\r\n * @param gameId Game ID\r\n * @returns Promise with slot details or null\r\n */ async function getEventGameSlotDetails(eventId, gameId) {\n    try {\n        const response = await fetch('https://ai.alviongs.com/webhook/v1/nibog/event-game-slot/get-all', {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        if (response.ok) {\n            const allSlots = await response.json();\n            const matchingSlot = allSlots.find((slot)=>slot.event_id === eventId && slot.game_id === gameId);\n            if (matchingSlot) {\n                return {\n                    slot_id: matchingSlot.id,\n                    custom_title: matchingSlot.custom_title,\n                    custom_description: matchingSlot.custom_description,\n                    custom_price: matchingSlot.custom_price,\n                    slot_price: matchingSlot.slot_price,\n                    start_time: matchingSlot.start_time,\n                    end_time: matchingSlot.end_time,\n                    max_participants: matchingSlot.max_participants\n                };\n            }\n        }\n    } catch (error) {\n        console.error('Error fetching slot details:', error);\n    }\n    return null;\n}\n/**\r\n * Get payment details for a booking\r\n * @param bookingId Booking ID\r\n * @returns Promise with payment details or null\r\n */ async function getBookingPaymentDetails(bookingId) {\n    try {\n        const response = await fetch('https://ai.alviongs.com/webhook/v1/nibog/payments/get-all', {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        if (response.ok) {\n            const allPayments = await response.json();\n            const payment = allPayments.find((p)=>p.booking_id === bookingId);\n            if (payment) {\n                return {\n                    payment_id: payment.payment_id,\n                    actual_payment_status: payment.payment_status,\n                    transaction_id: payment.transaction_id,\n                    payment_date: payment.payment_date,\n                    payment_method: payment.payment_method\n                };\n            }\n        }\n    } catch (error) {\n        console.error('Error fetching payment details:', error);\n    }\n    return null;\n}\n/**\r\n * Update booking payment status\r\n * @param bookingId Booking ID\r\n * @param paymentStatus New payment status\r\n * @returns Promise with updated booking data\r\n */ async function updateBookingPaymentStatus(bookingId, paymentStatus) {\n    try {\n        console.log(\"Updating booking \".concat(bookingId, \" payment status to: \").concat(paymentStatus));\n        // Use our internal API route that handles both payment status and booking status updates\n        const response = await fetch('/api/bookings/update-payment-status', {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                bookingId,\n                paymentStatus\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(\"Failed to update booking payment status: \".concat(response.status, \" - \").concat(errorText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error('Error updating booking payment status:', error);\n        throw error;\n    }\n}\n/**\r\n * Create a new booking\r\n * @param bookingData The booking data to create\r\n * @returns Promise with the created booking data\r\n */ async function createBooking(bookingData) {\n    try {\n        // Use our internal API route to avoid CORS issues\n        const response = await fetch('/api/bookings/create', {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(bookingData)\n        });\n        if (!response.ok) {\n            throw new Error(\"API returned error status: \".concat(response.status));\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./services/bookingService.ts\n"));

/***/ })

}]);