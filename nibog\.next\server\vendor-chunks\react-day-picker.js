"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-day-picker";
exports.ids = ["vendor-chunks/react-day-picker"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-day-picker/dist/index.esm.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-day-picker/dist/index.esm.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   Caption: () => (/* binding */ Caption),\n/* harmony export */   CaptionDropdowns: () => (/* binding */ CaptionDropdowns),\n/* harmony export */   CaptionLabel: () => (/* binding */ CaptionLabel),\n/* harmony export */   CaptionNavigation: () => (/* binding */ CaptionNavigation),\n/* harmony export */   Day: () => (/* binding */ Day),\n/* harmony export */   DayContent: () => (/* binding */ DayContent),\n/* harmony export */   DayPicker: () => (/* binding */ DayPicker),\n/* harmony export */   DayPickerContext: () => (/* binding */ DayPickerContext),\n/* harmony export */   DayPickerProvider: () => (/* binding */ DayPickerProvider),\n/* harmony export */   Dropdown: () => (/* binding */ Dropdown),\n/* harmony export */   FocusContext: () => (/* binding */ FocusContext),\n/* harmony export */   FocusProvider: () => (/* binding */ FocusProvider),\n/* harmony export */   Footer: () => (/* binding */ Footer),\n/* harmony export */   Head: () => (/* binding */ Head),\n/* harmony export */   HeadRow: () => (/* binding */ HeadRow),\n/* harmony export */   IconDropdown: () => (/* binding */ IconDropdown),\n/* harmony export */   IconLeft: () => (/* binding */ IconLeft),\n/* harmony export */   IconRight: () => (/* binding */ IconRight),\n/* harmony export */   InternalModifier: () => (/* binding */ InternalModifier),\n/* harmony export */   Months: () => (/* binding */ Months),\n/* harmony export */   NavigationContext: () => (/* binding */ NavigationContext),\n/* harmony export */   NavigationProvider: () => (/* binding */ NavigationProvider),\n/* harmony export */   RootProvider: () => (/* binding */ RootProvider),\n/* harmony export */   Row: () => (/* binding */ Row),\n/* harmony export */   SelectMultipleContext: () => (/* binding */ SelectMultipleContext),\n/* harmony export */   SelectMultipleProvider: () => (/* binding */ SelectMultipleProvider),\n/* harmony export */   SelectMultipleProviderInternal: () => (/* binding */ SelectMultipleProviderInternal),\n/* harmony export */   SelectRangeContext: () => (/* binding */ SelectRangeContext),\n/* harmony export */   SelectRangeProvider: () => (/* binding */ SelectRangeProvider),\n/* harmony export */   SelectRangeProviderInternal: () => (/* binding */ SelectRangeProviderInternal),\n/* harmony export */   SelectSingleContext: () => (/* binding */ SelectSingleContext),\n/* harmony export */   SelectSingleProvider: () => (/* binding */ SelectSingleProvider),\n/* harmony export */   SelectSingleProviderInternal: () => (/* binding */ SelectSingleProviderInternal),\n/* harmony export */   WeekNumber: () => (/* binding */ WeekNumber),\n/* harmony export */   addToRange: () => (/* binding */ addToRange),\n/* harmony export */   isDateAfterType: () => (/* binding */ isDateAfterType),\n/* harmony export */   isDateBeforeType: () => (/* binding */ isDateBeforeType),\n/* harmony export */   isDateInterval: () => (/* binding */ isDateInterval),\n/* harmony export */   isDateRange: () => (/* binding */ isDateRange),\n/* harmony export */   isDayOfWeekType: () => (/* binding */ isDayOfWeekType),\n/* harmony export */   isDayPickerDefault: () => (/* binding */ isDayPickerDefault),\n/* harmony export */   isDayPickerMultiple: () => (/* binding */ isDayPickerMultiple),\n/* harmony export */   isDayPickerRange: () => (/* binding */ isDayPickerRange),\n/* harmony export */   isDayPickerSingle: () => (/* binding */ isDayPickerSingle),\n/* harmony export */   isMatch: () => (/* binding */ isMatch),\n/* harmony export */   useActiveModifiers: () => (/* binding */ useActiveModifiers),\n/* harmony export */   useDayPicker: () => (/* binding */ useDayPicker),\n/* harmony export */   useDayRender: () => (/* binding */ useDayRender),\n/* harmony export */   useFocusContext: () => (/* binding */ useFocusContext),\n/* harmony export */   useInput: () => (/* binding */ useInput),\n/* harmony export */   useNavigation: () => (/* binding */ useNavigation),\n/* harmony export */   useSelectMultiple: () => (/* binding */ useSelectMultiple),\n/* harmony export */   useSelectRange: () => (/* binding */ useSelectRange),\n/* harmony export */   useSelectSingle: () => (/* binding */ useSelectSingle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfMonth.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/endOfMonth.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfDay.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isSameYear.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/setMonth.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/setYear.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfYear.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/differenceInCalendarMonths.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addMonths.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isSameMonth.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfISOWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addDays.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isSameDay.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/subDays.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/differenceInCalendarDays.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isDate.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addWeeks.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addYears.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/endOfISOWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/endOfWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/max.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/min.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getUnixTime.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getISOWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getWeeksInMonth.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/parse.mjs\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! date-fns/locale */ \"(ssr)/./node_modules/date-fns/locale/en-US.mjs\");\n\n\n\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nfunction __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/** Returns true when the props are of type {@link DayPickerMultipleProps}. */\nfunction isDayPickerMultiple(props) {\n    return props.mode === 'multiple';\n}\n\n/** Returns true when the props are of type {@link DayPickerRangeProps}. */\nfunction isDayPickerRange(props) {\n    return props.mode === 'range';\n}\n\n/** Returns true when the props are of type {@link DayPickerSingleProps}. */\nfunction isDayPickerSingle(props) {\n    return props.mode === 'single';\n}\n\n/**\n * The name of the default CSS classes.\n */\nvar defaultClassNames = {\n    root: 'rdp',\n    multiple_months: 'rdp-multiple_months',\n    with_weeknumber: 'rdp-with_weeknumber',\n    vhidden: 'rdp-vhidden',\n    button_reset: 'rdp-button_reset',\n    button: 'rdp-button',\n    caption: 'rdp-caption',\n    caption_start: 'rdp-caption_start',\n    caption_end: 'rdp-caption_end',\n    caption_between: 'rdp-caption_between',\n    caption_label: 'rdp-caption_label',\n    caption_dropdowns: 'rdp-caption_dropdowns',\n    dropdown: 'rdp-dropdown',\n    dropdown_month: 'rdp-dropdown_month',\n    dropdown_year: 'rdp-dropdown_year',\n    dropdown_icon: 'rdp-dropdown_icon',\n    months: 'rdp-months',\n    month: 'rdp-month',\n    table: 'rdp-table',\n    tbody: 'rdp-tbody',\n    tfoot: 'rdp-tfoot',\n    head: 'rdp-head',\n    head_row: 'rdp-head_row',\n    head_cell: 'rdp-head_cell',\n    nav: 'rdp-nav',\n    nav_button: 'rdp-nav_button',\n    nav_button_previous: 'rdp-nav_button_previous',\n    nav_button_next: 'rdp-nav_button_next',\n    nav_icon: 'rdp-nav_icon',\n    row: 'rdp-row',\n    weeknumber: 'rdp-weeknumber',\n    cell: 'rdp-cell',\n    day: 'rdp-day',\n    day_today: 'rdp-day_today',\n    day_outside: 'rdp-day_outside',\n    day_selected: 'rdp-day_selected',\n    day_disabled: 'rdp-day_disabled',\n    day_hidden: 'rdp-day_hidden',\n    day_range_start: 'rdp-day_range_start',\n    day_range_end: 'rdp-day_range_end',\n    day_range_middle: 'rdp-day_range_middle'\n};\n\n/**\n * The default formatter for the caption.\n */\nfunction formatCaption(month, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(month, 'LLLL y', options);\n}\n\n/**\n * The default formatter for the Day button.\n */\nfunction formatDay(day, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, 'd', options);\n}\n\n/**\n * The default formatter for the Month caption.\n */\nfunction formatMonthCaption(month, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(month, 'LLLL', options);\n}\n\n/**\n * The default formatter for the week number.\n */\nfunction formatWeekNumber(weekNumber) {\n    return \"\".concat(weekNumber);\n}\n\n/**\n * The default formatter for the name of the weekday.\n */\nfunction formatWeekdayName(weekday, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(weekday, 'cccccc', options);\n}\n\n/**\n * The default formatter for the Year caption.\n */\nfunction formatYearCaption(year, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(year, 'yyyy', options);\n}\n\nvar formatters = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    formatCaption: formatCaption,\n    formatDay: formatDay,\n    formatMonthCaption: formatMonthCaption,\n    formatWeekNumber: formatWeekNumber,\n    formatWeekdayName: formatWeekdayName,\n    formatYearCaption: formatYearCaption\n});\n\n/**\n * The default ARIA label for the day button.\n */\nvar labelDay = function (day, activeModifiers, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, 'do MMMM (EEEE)', options);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelMonthDropdown = function () {\n    return 'Month: ';\n};\n\n/**\n * The default ARIA label for next month button in navigation\n */\nvar labelNext = function () {\n    return 'Go to next month';\n};\n\n/**\n * The default ARIA label for previous month button in navigation\n */\nvar labelPrevious = function () {\n    return 'Go to previous month';\n};\n\n/**\n * The default ARIA label for the Weekday element.\n */\nvar labelWeekday = function (day, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, 'cccc', options);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelWeekNumber = function (n) {\n    return \"Week n. \".concat(n);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelYearDropdown = function () {\n    return 'Year: ';\n};\n\nvar labels = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    labelDay: labelDay,\n    labelMonthDropdown: labelMonthDropdown,\n    labelNext: labelNext,\n    labelPrevious: labelPrevious,\n    labelWeekNumber: labelWeekNumber,\n    labelWeekday: labelWeekday,\n    labelYearDropdown: labelYearDropdown\n});\n\n/**\n * Returns the default values to use in the DayPickerContext, in case they are\n * not passed down with the DayPicker initial props.\n */\nfunction getDefaultContextValues() {\n    var captionLayout = 'buttons';\n    var classNames = defaultClassNames;\n    var locale = date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.enUS;\n    var modifiersClassNames = {};\n    var modifiers = {};\n    var numberOfMonths = 1;\n    var styles = {};\n    var today = new Date();\n    return {\n        captionLayout: captionLayout,\n        classNames: classNames,\n        formatters: formatters,\n        labels: labels,\n        locale: locale,\n        modifiersClassNames: modifiersClassNames,\n        modifiers: modifiers,\n        numberOfMonths: numberOfMonths,\n        styles: styles,\n        today: today,\n        mode: 'default'\n    };\n}\n\n/** Return the `fromDate` and `toDate` prop values values parsing the DayPicker props. */\nfunction parseFromToProps(props) {\n    var fromYear = props.fromYear, toYear = props.toYear, fromMonth = props.fromMonth, toMonth = props.toMonth;\n    var fromDate = props.fromDate, toDate = props.toDate;\n    if (fromMonth) {\n        fromDate = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(fromMonth);\n    }\n    else if (fromYear) {\n        fromDate = new Date(fromYear, 0, 1);\n    }\n    if (toMonth) {\n        toDate = (0,date_fns__WEBPACK_IMPORTED_MODULE_5__.endOfMonth)(toMonth);\n    }\n    else if (toYear) {\n        toDate = new Date(toYear, 11, 31);\n    }\n    return {\n        fromDate: fromDate ? (0,date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(fromDate) : undefined,\n        toDate: toDate ? (0,date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(toDate) : undefined\n    };\n}\n\n/**\n * The DayPicker context shares the props passed to DayPicker within internal\n * and custom components. It is used to set the default values and perform\n * one-time calculations required to render the days.\n *\n * Access to this context from the {@link useDayPicker} hook.\n */\nvar DayPickerContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/**\n * The provider for the {@link DayPickerContext}, assigning the defaults from the\n * initial DayPicker props.\n */\nfunction DayPickerProvider(props) {\n    var _a;\n    var initialProps = props.initialProps;\n    var defaultContextValues = getDefaultContextValues();\n    var _b = parseFromToProps(initialProps), fromDate = _b.fromDate, toDate = _b.toDate;\n    var captionLayout = (_a = initialProps.captionLayout) !== null && _a !== void 0 ? _a : defaultContextValues.captionLayout;\n    if (captionLayout !== 'buttons' && (!fromDate || !toDate)) {\n        // When no from/to dates are set, the caption is always buttons\n        captionLayout = 'buttons';\n    }\n    var onSelect;\n    if (isDayPickerSingle(initialProps) ||\n        isDayPickerMultiple(initialProps) ||\n        isDayPickerRange(initialProps)) {\n        onSelect = initialProps.onSelect;\n    }\n    var value = __assign(__assign(__assign({}, defaultContextValues), initialProps), { captionLayout: captionLayout, classNames: __assign(__assign({}, defaultContextValues.classNames), initialProps.classNames), components: __assign({}, initialProps.components), formatters: __assign(__assign({}, defaultContextValues.formatters), initialProps.formatters), fromDate: fromDate, labels: __assign(__assign({}, defaultContextValues.labels), initialProps.labels), mode: initialProps.mode || defaultContextValues.mode, modifiers: __assign(__assign({}, defaultContextValues.modifiers), initialProps.modifiers), modifiersClassNames: __assign(__assign({}, defaultContextValues.modifiersClassNames), initialProps.modifiersClassNames), onSelect: onSelect, styles: __assign(__assign({}, defaultContextValues.styles), initialProps.styles), toDate: toDate });\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayPickerContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link DayPickerContextValue}.\n *\n * Use the DayPicker context to access to the props passed to DayPicker inside\n * internal or custom components.\n */\nfunction useDayPicker() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(DayPickerContext);\n    if (!context) {\n        throw new Error(\"useDayPicker must be used within a DayPickerProvider.\");\n    }\n    return context;\n}\n\n/** Render the caption for the displayed month. This component is used when `captionLayout=\"buttons\"`. */\nfunction CaptionLabel(props) {\n    var _a = useDayPicker(), locale = _a.locale, classNames = _a.classNames, styles = _a.styles, formatCaption = _a.formatters.formatCaption;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.caption_label, style: styles.caption_label, \"aria-live\": \"polite\", role: \"presentation\", id: props.id, children: formatCaption(props.displayMonth, { locale: locale }) }));\n}\n\n/**\n * Render the icon in the styled drop-down.\n */\nfunction IconDropdown(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", __assign({ width: \"8px\", height: \"8px\", viewBox: \"0 0 120 120\", \"data-testid\": \"iconDropdown\" }, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.0739418023,59.4824074 -0.0739418023,52.5175926 4.22182541,48.2218254 Z\", fill: \"currentColor\", fillRule: \"nonzero\" }) })));\n}\n\n/**\n * Render a styled select component – displaying a caption and a custom\n * drop-down icon.\n */\nfunction Dropdown(props) {\n    var _a, _b;\n    var onChange = props.onChange, value = props.value, children = props.children, caption = props.caption, className = props.className, style = props.style;\n    var dayPicker = useDayPicker();\n    var IconDropdownComponent = (_b = (_a = dayPicker.components) === null || _a === void 0 ? void 0 : _a.IconDropdown) !== null && _b !== void 0 ? _b : IconDropdown;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: className, style: style, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: dayPicker.classNames.vhidden, children: props['aria-label'] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"select\", { name: props.name, \"aria-label\": props['aria-label'], className: dayPicker.classNames.dropdown, style: dayPicker.styles.dropdown, value: value, onChange: onChange, children: children }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: dayPicker.classNames.caption_label, style: dayPicker.styles.caption_label, \"aria-hidden\": \"true\", children: [caption, (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconDropdownComponent, { className: dayPicker.classNames.dropdown_icon, style: dayPicker.styles.dropdown_icon })] })] }));\n}\n\n/** Render the dropdown to navigate between months. */\nfunction MonthsDropdown(props) {\n    var _a;\n    var _b = useDayPicker(), fromDate = _b.fromDate, toDate = _b.toDate, styles = _b.styles, locale = _b.locale, formatMonthCaption = _b.formatters.formatMonthCaption, classNames = _b.classNames, components = _b.components, labelMonthDropdown = _b.labels.labelMonthDropdown;\n    // Dropdown should appear only when both from/toDate is set\n    if (!fromDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    if (!toDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    var dropdownMonths = [];\n    if ((0,date_fns__WEBPACK_IMPORTED_MODULE_7__.isSameYear)(fromDate, toDate)) {\n        // only display the months included in the range\n        var date = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(fromDate);\n        for (var month = fromDate.getMonth(); month <= toDate.getMonth(); month++) {\n            dropdownMonths.push((0,date_fns__WEBPACK_IMPORTED_MODULE_8__.setMonth)(date, month));\n        }\n    }\n    else {\n        // display all the 12 months\n        var date = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(new Date()); // Any date should be OK, as we just need the year\n        for (var month = 0; month <= 11; month++) {\n            dropdownMonths.push((0,date_fns__WEBPACK_IMPORTED_MODULE_8__.setMonth)(date, month));\n        }\n    }\n    var handleChange = function (e) {\n        var selectedMonth = Number(e.target.value);\n        var newMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_8__.setMonth)((0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(props.displayMonth), selectedMonth);\n        props.onChange(newMonth);\n    };\n    var DropdownComponent = (_a = components === null || components === void 0 ? void 0 : components.Dropdown) !== null && _a !== void 0 ? _a : Dropdown;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DropdownComponent, { name: \"months\", \"aria-label\": labelMonthDropdown(), className: classNames.dropdown_month, style: styles.dropdown_month, onChange: handleChange, value: props.displayMonth.getMonth(), caption: formatMonthCaption(props.displayMonth, { locale: locale }), children: dropdownMonths.map(function (m) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"option\", { value: m.getMonth(), children: formatMonthCaption(m, { locale: locale }) }, m.getMonth())); }) }));\n}\n\n/**\n * Render a dropdown to change the year. Take in account the `nav.fromDate` and\n * `toDate` from context.\n */\nfunction YearsDropdown(props) {\n    var _a;\n    var displayMonth = props.displayMonth;\n    var _b = useDayPicker(), fromDate = _b.fromDate, toDate = _b.toDate, locale = _b.locale, styles = _b.styles, classNames = _b.classNames, components = _b.components, formatYearCaption = _b.formatters.formatYearCaption, labelYearDropdown = _b.labels.labelYearDropdown;\n    var years = [];\n    // Dropdown should appear only when both from/toDate is set\n    if (!fromDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    if (!toDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    var fromYear = fromDate.getFullYear();\n    var toYear = toDate.getFullYear();\n    for (var year = fromYear; year <= toYear; year++) {\n        years.push((0,date_fns__WEBPACK_IMPORTED_MODULE_9__.setYear)((0,date_fns__WEBPACK_IMPORTED_MODULE_10__.startOfYear)(new Date()), year));\n    }\n    var handleChange = function (e) {\n        var newMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_9__.setYear)((0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(displayMonth), Number(e.target.value));\n        props.onChange(newMonth);\n    };\n    var DropdownComponent = (_a = components === null || components === void 0 ? void 0 : components.Dropdown) !== null && _a !== void 0 ? _a : Dropdown;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DropdownComponent, { name: \"years\", \"aria-label\": labelYearDropdown(), className: classNames.dropdown_year, style: styles.dropdown_year, onChange: handleChange, value: displayMonth.getFullYear(), caption: formatYearCaption(displayMonth, { locale: locale }), children: years.map(function (year) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"option\", { value: year.getFullYear(), children: formatYearCaption(year, { locale: locale }) }, year.getFullYear())); }) }));\n}\n\n/**\n * Helper hook for using controlled/uncontrolled values from a component props.\n *\n * When the value is not controlled, pass `undefined` as `controlledValue` and\n * use the returned setter to update it.\n *\n * When the value is controlled, pass the controlled value as second\n * argument, which will be always returned as `value`.\n */\nfunction useControlledValue(defaultValue, controlledValue) {\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultValue), uncontrolledValue = _a[0], setValue = _a[1];\n    var value = controlledValue === undefined ? uncontrolledValue : controlledValue;\n    return [value, setValue];\n}\n\n/** Return the initial month according to the given options. */\nfunction getInitialMonth(context) {\n    var month = context.month, defaultMonth = context.defaultMonth, today = context.today;\n    var initialMonth = month || defaultMonth || today || new Date();\n    var toDate = context.toDate, fromDate = context.fromDate, _a = context.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    // Fix the initialMonth if is after the to-date\n    if (toDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(toDate, initialMonth) < 0) {\n        var offset = -1 * (numberOfMonths - 1);\n        initialMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(toDate, offset);\n    }\n    // Fix the initialMonth if is before the from-date\n    if (fromDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(initialMonth, fromDate) < 0) {\n        initialMonth = fromDate;\n    }\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(initialMonth);\n}\n\n/** Controls the navigation state. */\nfunction useNavigationState() {\n    var context = useDayPicker();\n    var initialMonth = getInitialMonth(context);\n    var _a = useControlledValue(initialMonth, context.month), month = _a[0], setMonth = _a[1];\n    var goToMonth = function (date) {\n        var _a;\n        if (context.disableNavigation)\n            return;\n        var month = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(date);\n        setMonth(month);\n        (_a = context.onMonthChange) === null || _a === void 0 ? void 0 : _a.call(context, month);\n    };\n    return [month, goToMonth];\n}\n\n/**\n * Return the months to display in the component according to the number of\n * months and the from/to date.\n */\nfunction getDisplayMonths(month, _a) {\n    var reverseMonths = _a.reverseMonths, numberOfMonths = _a.numberOfMonths;\n    var start = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(month);\n    var end = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)((0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(start, numberOfMonths));\n    var monthsDiff = (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(end, start);\n    var months = [];\n    for (var i = 0; i < monthsDiff; i++) {\n        var nextMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(start, i);\n        months.push(nextMonth);\n    }\n    if (reverseMonths)\n        months = months.reverse();\n    return months;\n}\n\n/**\n * Returns the next month the user can navigate to according to the given\n * options.\n *\n * Please note that the next month is not always the next calendar month:\n *\n * - if after the `toDate` range, is undefined;\n * - if the navigation is paged, is the number of months displayed ahead.\n *\n */\nfunction getNextMonth(startingMonth, options) {\n    if (options.disableNavigation) {\n        return undefined;\n    }\n    var toDate = options.toDate, pagedNavigation = options.pagedNavigation, _a = options.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    var offset = pagedNavigation ? numberOfMonths : 1;\n    var month = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(startingMonth);\n    if (!toDate) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, offset);\n    }\n    var monthsDiff = (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(toDate, startingMonth);\n    if (monthsDiff < numberOfMonths) {\n        return undefined;\n    }\n    // Jump forward as the number of months when paged navigation\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, offset);\n}\n\n/**\n * Returns the next previous the user can navigate to, according to the given\n * options.\n *\n * Please note that the previous month is not always the previous calendar\n * month:\n *\n * - if before the `fromDate` date, is `undefined`;\n * - if the navigation is paged, is the number of months displayed before.\n *\n */\nfunction getPreviousMonth(startingMonth, options) {\n    if (options.disableNavigation) {\n        return undefined;\n    }\n    var fromDate = options.fromDate, pagedNavigation = options.pagedNavigation, _a = options.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    var offset = pagedNavigation ? numberOfMonths : 1;\n    var month = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(startingMonth);\n    if (!fromDate) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, -offset);\n    }\n    var monthsDiff = (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(month, fromDate);\n    if (monthsDiff <= 0) {\n        return undefined;\n    }\n    // Jump back as the number of months when paged navigation\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, -offset);\n}\n\n/**\n * The Navigation context shares details and methods to navigate the months in DayPicker.\n * Access this context from the {@link useNavigation} hook.\n */\nvar NavigationContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link NavigationContext}. */\nfunction NavigationProvider(props) {\n    var dayPicker = useDayPicker();\n    var _a = useNavigationState(), currentMonth = _a[0], goToMonth = _a[1];\n    var displayMonths = getDisplayMonths(currentMonth, dayPicker);\n    var nextMonth = getNextMonth(currentMonth, dayPicker);\n    var previousMonth = getPreviousMonth(currentMonth, dayPicker);\n    var isDateDisplayed = function (date) {\n        return displayMonths.some(function (displayMonth) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_13__.isSameMonth)(date, displayMonth);\n        });\n    };\n    var goToDate = function (date, refDate) {\n        if (isDateDisplayed(date)) {\n            return;\n        }\n        if (refDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_14__.isBefore)(date, refDate)) {\n            goToMonth((0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(date, 1 + dayPicker.numberOfMonths * -1));\n        }\n        else {\n            goToMonth(date);\n        }\n    };\n    var value = {\n        currentMonth: currentMonth,\n        displayMonths: displayMonths,\n        goToMonth: goToMonth,\n        goToDate: goToDate,\n        previousMonth: previousMonth,\n        nextMonth: nextMonth,\n        isDateDisplayed: isDateDisplayed\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(NavigationContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link NavigationContextValue}. Use this hook to navigate\n * between months or years in DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useNavigation() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NavigationContext);\n    if (!context) {\n        throw new Error('useNavigation must be used within a NavigationProvider');\n    }\n    return context;\n}\n\n/**\n * Render a caption with the dropdowns to navigate between months and years.\n */\nfunction CaptionDropdowns(props) {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, styles = _b.styles, components = _b.components;\n    var goToMonth = useNavigation().goToMonth;\n    var handleMonthChange = function (newMonth) {\n        goToMonth((0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(newMonth, props.displayIndex ? -props.displayIndex : 0));\n    };\n    var CaptionLabelComponent = (_a = components === null || components === void 0 ? void 0 : components.CaptionLabel) !== null && _a !== void 0 ? _a : CaptionLabel;\n    var captionLabel = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth }));\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: classNames.caption_dropdowns, style: styles.caption_dropdowns, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.vhidden, children: captionLabel }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MonthsDropdown, { onChange: handleMonthChange, displayMonth: props.displayMonth }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(YearsDropdown, { onChange: handleMonthChange, displayMonth: props.displayMonth })] }));\n}\n\n/**\n * Render the \"previous month\" button in the navigation.\n */\nfunction IconLeft(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", __assign({ width: \"16px\", height: \"16px\", viewBox: \"0 0 120 120\" }, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z\", fill: \"currentColor\", fillRule: \"nonzero\" }) })));\n}\n\n/**\n * Render the \"next month\" button in the navigation.\n */\nfunction IconRight(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", __assign({ width: \"16px\", height: \"16px\", viewBox: \"0 0 120 120\" }, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z\", fill: \"currentColor\" }) })));\n}\n\n/** Render a button HTML element applying the reset class name. */\nvar Button = (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function (props, ref) {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles;\n    var classNamesArr = [classNames.button_reset, classNames.button];\n    if (props.className) {\n        classNamesArr.push(props.className);\n    }\n    var className = classNamesArr.join(' ');\n    var style = __assign(__assign({}, styles.button_reset), styles.button);\n    if (props.style) {\n        Object.assign(style, props.style);\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", __assign({}, props, { ref: ref, type: \"button\", className: className, style: style })));\n});\n\n/** A component rendering the navigation buttons or the drop-downs. */\nfunction Navigation(props) {\n    var _a, _b;\n    var _c = useDayPicker(), dir = _c.dir, locale = _c.locale, classNames = _c.classNames, styles = _c.styles, _d = _c.labels, labelPrevious = _d.labelPrevious, labelNext = _d.labelNext, components = _c.components;\n    if (!props.nextMonth && !props.previousMonth) {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    }\n    var previousLabel = labelPrevious(props.previousMonth, { locale: locale });\n    var previousClassName = [\n        classNames.nav_button,\n        classNames.nav_button_previous\n    ].join(' ');\n    var nextLabel = labelNext(props.nextMonth, { locale: locale });\n    var nextClassName = [\n        classNames.nav_button,\n        classNames.nav_button_next\n    ].join(' ');\n    var IconRightComponent = (_a = components === null || components === void 0 ? void 0 : components.IconRight) !== null && _a !== void 0 ? _a : IconRight;\n    var IconLeftComponent = (_b = components === null || components === void 0 ? void 0 : components.IconLeft) !== null && _b !== void 0 ? _b : IconLeft;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: classNames.nav, style: styles.nav, children: [!props.hidePrevious && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, { name: \"previous-month\", \"aria-label\": previousLabel, className: previousClassName, style: styles.nav_button_previous, disabled: !props.previousMonth, onClick: props.onPreviousClick, children: dir === 'rtl' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconRightComponent, { className: classNames.nav_icon, style: styles.nav_icon })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconLeftComponent, { className: classNames.nav_icon, style: styles.nav_icon })) })), !props.hideNext && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, { name: \"next-month\", \"aria-label\": nextLabel, className: nextClassName, style: styles.nav_button_next, disabled: !props.nextMonth, onClick: props.onNextClick, children: dir === 'rtl' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconLeftComponent, { className: classNames.nav_icon, style: styles.nav_icon })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconRightComponent, { className: classNames.nav_icon, style: styles.nav_icon })) }))] }));\n}\n\n/**\n * Render a caption with a button-based navigation.\n */\nfunction CaptionNavigation(props) {\n    var numberOfMonths = useDayPicker().numberOfMonths;\n    var _a = useNavigation(), previousMonth = _a.previousMonth, nextMonth = _a.nextMonth, goToMonth = _a.goToMonth, displayMonths = _a.displayMonths;\n    var displayIndex = displayMonths.findIndex(function (month) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_13__.isSameMonth)(props.displayMonth, month);\n    });\n    var isFirst = displayIndex === 0;\n    var isLast = displayIndex === displayMonths.length - 1;\n    var hideNext = numberOfMonths > 1 && (isFirst || !isLast);\n    var hidePrevious = numberOfMonths > 1 && (isLast || !isFirst);\n    var handlePreviousClick = function () {\n        if (!previousMonth)\n            return;\n        goToMonth(previousMonth);\n    };\n    var handleNextClick = function () {\n        if (!nextMonth)\n            return;\n        goToMonth(nextMonth);\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Navigation, { displayMonth: props.displayMonth, hideNext: hideNext, hidePrevious: hidePrevious, nextMonth: nextMonth, previousMonth: previousMonth, onPreviousClick: handlePreviousClick, onNextClick: handleNextClick }));\n}\n\n/**\n * Render the caption of a month. The caption has a different layout when\n * setting the {@link DayPickerBase.captionLayout} prop.\n */\nfunction Caption(props) {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, disableNavigation = _b.disableNavigation, styles = _b.styles, captionLayout = _b.captionLayout, components = _b.components;\n    var CaptionLabelComponent = (_a = components === null || components === void 0 ? void 0 : components.CaptionLabel) !== null && _a !== void 0 ? _a : CaptionLabel;\n    var caption;\n    if (disableNavigation) {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth }));\n    }\n    else if (captionLayout === 'dropdown') {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionDropdowns, { displayMonth: props.displayMonth, id: props.id }));\n    }\n    else if (captionLayout === 'dropdown-buttons') {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionDropdowns, { displayMonth: props.displayMonth, displayIndex: props.displayIndex, id: props.id }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionNavigation, { displayMonth: props.displayMonth, displayIndex: props.displayIndex, id: props.id })] }));\n    }\n    else {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth, displayIndex: props.displayIndex }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionNavigation, { displayMonth: props.displayMonth, id: props.id })] }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.caption, style: styles.caption, children: caption }));\n}\n\n/** Render the Footer component (empty as default).*/\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction Footer(props) {\n    var _a = useDayPicker(), footer = _a.footer, styles = _a.styles, tfoot = _a.classNames.tfoot;\n    if (!footer)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tfoot\", { className: tfoot, style: styles.tfoot, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tr\", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { colSpan: 8, children: footer }) }) }));\n}\n\n/**\n * Generate a series of 7 days, starting from the week, to use for formatting\n * the weekday names (Monday, Tuesday, etc.).\n */\nfunction getWeekdays(locale, \n/** The index of the first day of the week (0 - Sunday). */\nweekStartsOn, \n/** Use ISOWeek instead of locale/ */\nISOWeek) {\n    var start = ISOWeek\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_15__.startOfISOWeek)(new Date())\n        : (0,date_fns__WEBPACK_IMPORTED_MODULE_16__.startOfWeek)(new Date(), { locale: locale, weekStartsOn: weekStartsOn });\n    var days = [];\n    for (var i = 0; i < 7; i++) {\n        var day = (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(start, i);\n        days.push(day);\n    }\n    return days;\n}\n\n/**\n * Render the HeadRow component - i.e. the table head row with the weekday names.\n */\nfunction HeadRow() {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles, showWeekNumber = _a.showWeekNumber, locale = _a.locale, weekStartsOn = _a.weekStartsOn, ISOWeek = _a.ISOWeek, formatWeekdayName = _a.formatters.formatWeekdayName, labelWeekday = _a.labels.labelWeekday;\n    var weekdays = getWeekdays(locale, weekStartsOn, ISOWeek);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"tr\", { style: styles.head_row, className: classNames.head_row, children: [showWeekNumber && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { style: styles.head_cell, className: classNames.head_cell })), weekdays.map(function (weekday, i) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"th\", { scope: \"col\", className: classNames.head_cell, style: styles.head_cell, \"aria-label\": labelWeekday(weekday, { locale: locale }), children: formatWeekdayName(weekday, { locale: locale }) }, i)); })] }));\n}\n\n/** Render the table head. */\nfunction Head() {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, styles = _b.styles, components = _b.components;\n    var HeadRowComponent = (_a = components === null || components === void 0 ? void 0 : components.HeadRow) !== null && _a !== void 0 ? _a : HeadRow;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"thead\", { style: styles.head, className: classNames.head, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(HeadRowComponent, {}) }));\n}\n\n/** Render the content of the day cell. */\nfunction DayContent(props) {\n    var _a = useDayPicker(), locale = _a.locale, formatDay = _a.formatters.formatDay;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: formatDay(props.date, { locale: locale }) });\n}\n\n/**\n * The SelectMultiple context shares details about the selected days when in\n * multiple selection mode.\n *\n * Access this context from the {@link useSelectMultiple} hook.\n */\nvar SelectMultipleContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link SelectMultipleContext}. */\nfunction SelectMultipleProvider(props) {\n    if (!isDayPickerMultiple(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined,\n            modifiers: {\n                disabled: []\n            }\n        };\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectMultipleProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var selected = initialProps.selected, min = initialProps.min, max = initialProps.max;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        var isMinSelected = Boolean(activeModifiers.selected && min && (selected === null || selected === void 0 ? void 0 : selected.length) === min);\n        if (isMinSelected) {\n            return;\n        }\n        var isMaxSelected = Boolean(!activeModifiers.selected && max && (selected === null || selected === void 0 ? void 0 : selected.length) === max);\n        if (isMaxSelected) {\n            return;\n        }\n        var selectedDays = selected ? __spreadArray([], selected, true) : [];\n        if (activeModifiers.selected) {\n            var index = selectedDays.findIndex(function (selectedDay) {\n                return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(day, selectedDay);\n            });\n            selectedDays.splice(index, 1);\n        }\n        else {\n            selectedDays.push(day);\n        }\n        (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, selectedDays, day, activeModifiers, e);\n    };\n    var modifiers = {\n        disabled: []\n    };\n    if (selected) {\n        modifiers.disabled.push(function (day) {\n            var isMaxSelected = max && selected.length > max - 1;\n            var isSelected = selected.some(function (selectedDay) {\n                return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(selectedDay, day);\n            });\n            return Boolean(isMaxSelected && !isSelected);\n        });\n    }\n    var contextValue = {\n        selected: selected,\n        onDayClick: onDayClick,\n        modifiers: modifiers\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleContext.Provider, { value: contextValue, children: children }));\n}\n/**\n * Hook to access the {@link SelectMultipleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectMultiple() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SelectMultipleContext);\n    if (!context) {\n        throw new Error('useSelectMultiple must be used within a SelectMultipleProvider');\n    }\n    return context;\n}\n\n/**\n * Add a day to an existing range.\n *\n * The returned range takes in account the `undefined` values and if the added\n * day is already present in the range.\n */\nfunction addToRange(day, range) {\n    var _a = range || {}, from = _a.from, to = _a.to;\n    if (from && to) {\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(to, day) && (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(from, day)) {\n            return undefined;\n        }\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(to, day)) {\n            return { from: to, to: undefined };\n        }\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(from, day)) {\n            return undefined;\n        }\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_19__.isAfter)(from, day)) {\n            return { from: day, to: to };\n        }\n        return { from: from, to: day };\n    }\n    if (to) {\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_19__.isAfter)(day, to)) {\n            return { from: to, to: day };\n        }\n        return { from: day, to: to };\n    }\n    if (from) {\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_14__.isBefore)(day, from)) {\n            return { from: day, to: from };\n        }\n        return { from: from, to: day };\n    }\n    return { from: day, to: undefined };\n}\n\n/**\n * The SelectRange context shares details about the selected days when in\n * range selection mode.\n *\n * Access this context from the {@link useSelectRange} hook.\n */\nvar SelectRangeContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link SelectRangeProvider}. */\nfunction SelectRangeProvider(props) {\n    if (!isDayPickerRange(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined,\n            modifiers: {\n                range_start: [],\n                range_end: [],\n                range_middle: [],\n                disabled: []\n            }\n        };\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectRangeProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var selected = initialProps.selected;\n    var _b = selected || {}, selectedFrom = _b.from, selectedTo = _b.to;\n    var min = initialProps.min;\n    var max = initialProps.max;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        var newRange = addToRange(day, selected);\n        (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, newRange, day, activeModifiers, e);\n    };\n    var modifiers = {\n        range_start: [],\n        range_end: [],\n        range_middle: [],\n        disabled: []\n    };\n    if (selectedFrom) {\n        modifiers.range_start = [selectedFrom];\n        if (!selectedTo) {\n            modifiers.range_end = [selectedFrom];\n        }\n        else {\n            modifiers.range_end = [selectedTo];\n            if (!(0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(selectedFrom, selectedTo)) {\n                modifiers.range_middle = [\n                    {\n                        after: selectedFrom,\n                        before: selectedTo\n                    }\n                ];\n            }\n        }\n    }\n    else if (selectedTo) {\n        modifiers.range_start = [selectedTo];\n        modifiers.range_end = [selectedTo];\n    }\n    if (min) {\n        if (selectedFrom && !selectedTo) {\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_20__.subDays)(selectedFrom, min - 1),\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, min - 1)\n            });\n        }\n        if (selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                after: selectedFrom,\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, min - 1)\n            });\n        }\n        if (!selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_20__.subDays)(selectedTo, min - 1),\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, min - 1)\n            });\n        }\n    }\n    if (max) {\n        if (selectedFrom && !selectedTo) {\n            modifiers.disabled.push({\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, -max + 1)\n            });\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, max - 1)\n            });\n        }\n        if (selectedFrom && selectedTo) {\n            var selectedCount = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(selectedTo, selectedFrom) + 1;\n            var offset = max - selectedCount;\n            modifiers.disabled.push({\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_20__.subDays)(selectedFrom, offset)\n            });\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, offset)\n            });\n        }\n        if (!selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, -max + 1)\n            });\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, max - 1)\n            });\n        }\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeContext.Provider, { value: { selected: selected, onDayClick: onDayClick, modifiers: modifiers }, children: children }));\n}\n/**\n * Hook to access the {@link SelectRangeContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectRange() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SelectRangeContext);\n    if (!context) {\n        throw new Error('useSelectRange must be used within a SelectRangeProvider');\n    }\n    return context;\n}\n\n/** Normalize to array a matcher input. */\nfunction matcherToArray(matcher) {\n    if (Array.isArray(matcher)) {\n        return __spreadArray([], matcher, true);\n    }\n    else if (matcher !== undefined) {\n        return [matcher];\n    }\n    else {\n        return [];\n    }\n}\n\n/** Create CustomModifiers from dayModifiers */\nfunction getCustomModifiers(dayModifiers) {\n    var customModifiers = {};\n    Object.entries(dayModifiers).forEach(function (_a) {\n        var modifier = _a[0], matcher = _a[1];\n        customModifiers[modifier] = matcherToArray(matcher);\n    });\n    return customModifiers;\n}\n\n/** The name of the modifiers that are used internally by DayPicker. */\nvar InternalModifier;\n(function (InternalModifier) {\n    InternalModifier[\"Outside\"] = \"outside\";\n    /** Name of the modifier applied to the disabled days, using the `disabled` prop. */\n    InternalModifier[\"Disabled\"] = \"disabled\";\n    /** Name of the modifier applied to the selected days using the `selected` prop). */\n    InternalModifier[\"Selected\"] = \"selected\";\n    /** Name of the modifier applied to the hidden days using the `hidden` prop). */\n    InternalModifier[\"Hidden\"] = \"hidden\";\n    /** Name of the modifier applied to the day specified using the `today` prop). */\n    InternalModifier[\"Today\"] = \"today\";\n    /** The modifier applied to the day starting a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeStart\"] = \"range_start\";\n    /** The modifier applied to the day ending a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeEnd\"] = \"range_end\";\n    /** The modifier applied to the days between the start and the end of a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeMiddle\"] = \"range_middle\";\n})(InternalModifier || (InternalModifier = {}));\n\nvar Selected = InternalModifier.Selected, Disabled = InternalModifier.Disabled, Hidden = InternalModifier.Hidden, Today = InternalModifier.Today, RangeEnd = InternalModifier.RangeEnd, RangeMiddle = InternalModifier.RangeMiddle, RangeStart = InternalModifier.RangeStart, Outside = InternalModifier.Outside;\n/** Return the {@link InternalModifiers} from the DayPicker and select contexts. */\nfunction getInternalModifiers(dayPicker, selectMultiple, selectRange) {\n    var _a;\n    var internalModifiers = (_a = {},\n        _a[Selected] = matcherToArray(dayPicker.selected),\n        _a[Disabled] = matcherToArray(dayPicker.disabled),\n        _a[Hidden] = matcherToArray(dayPicker.hidden),\n        _a[Today] = [dayPicker.today],\n        _a[RangeEnd] = [],\n        _a[RangeMiddle] = [],\n        _a[RangeStart] = [],\n        _a[Outside] = [],\n        _a);\n    if (dayPicker.fromDate) {\n        internalModifiers[Disabled].push({ before: dayPicker.fromDate });\n    }\n    if (dayPicker.toDate) {\n        internalModifiers[Disabled].push({ after: dayPicker.toDate });\n    }\n    if (isDayPickerMultiple(dayPicker)) {\n        internalModifiers[Disabled] = internalModifiers[Disabled].concat(selectMultiple.modifiers[Disabled]);\n    }\n    else if (isDayPickerRange(dayPicker)) {\n        internalModifiers[Disabled] = internalModifiers[Disabled].concat(selectRange.modifiers[Disabled]);\n        internalModifiers[RangeStart] = selectRange.modifiers[RangeStart];\n        internalModifiers[RangeMiddle] = selectRange.modifiers[RangeMiddle];\n        internalModifiers[RangeEnd] = selectRange.modifiers[RangeEnd];\n    }\n    return internalModifiers;\n}\n\n/** The Modifiers context store the modifiers used in DayPicker. To access the value of this context, use {@link useModifiers}. */\nvar ModifiersContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provide the value for the {@link ModifiersContext}. */\nfunction ModifiersProvider(props) {\n    var dayPicker = useDayPicker();\n    var selectMultiple = useSelectMultiple();\n    var selectRange = useSelectRange();\n    var internalModifiers = getInternalModifiers(dayPicker, selectMultiple, selectRange);\n    var customModifiers = getCustomModifiers(dayPicker.modifiers);\n    var modifiers = __assign(__assign({}, internalModifiers), customModifiers);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ModifiersContext.Provider, { value: modifiers, children: props.children }));\n}\n/**\n * Return the modifiers used by DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n * Requires to be wrapped into {@link ModifiersProvider}.\n *\n */\nfunction useModifiers() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ModifiersContext);\n    if (!context) {\n        throw new Error('useModifiers must be used within a ModifiersProvider');\n    }\n    return context;\n}\n\n/** Returns true if `matcher` is of type {@link DateInterval}. */\nfunction isDateInterval(matcher) {\n    return Boolean(matcher &&\n        typeof matcher === 'object' &&\n        'before' in matcher &&\n        'after' in matcher);\n}\n/** Returns true if `value` is a {@link DateRange} type. */\nfunction isDateRange(value) {\n    return Boolean(value && typeof value === 'object' && 'from' in value);\n}\n/** Returns true if `value` is of type {@link DateAfter}. */\nfunction isDateAfterType(value) {\n    return Boolean(value && typeof value === 'object' && 'after' in value);\n}\n/** Returns true if `value` is of type {@link DateBefore}. */\nfunction isDateBeforeType(value) {\n    return Boolean(value && typeof value === 'object' && 'before' in value);\n}\n/** Returns true if `value` is a {@link DayOfWeek} type. */\nfunction isDayOfWeekType(value) {\n    return Boolean(value && typeof value === 'object' && 'dayOfWeek' in value);\n}\n\n/** Return `true` whether `date` is inside `range`. */\nfunction isDateInRange(date, range) {\n    var _a;\n    var from = range.from, to = range.to;\n    if (from && to) {\n        var isRangeInverted = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(to, from) < 0;\n        if (isRangeInverted) {\n            _a = [to, from], from = _a[0], to = _a[1];\n        }\n        var isInRange = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(date, from) >= 0 &&\n            (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(to, date) >= 0;\n        return isInRange;\n    }\n    if (to) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(to, date);\n    }\n    if (from) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(from, date);\n    }\n    return false;\n}\n\n/** Returns true if `value` is a Date type. */\nfunction isDateType(value) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_22__.isDate)(value);\n}\n/** Returns true if `value` is an array of valid dates. */\nfunction isArrayOfDates(value) {\n    return Array.isArray(value) && value.every(date_fns__WEBPACK_IMPORTED_MODULE_22__.isDate);\n}\n/**\n * Returns whether a day matches against at least one of the given Matchers.\n *\n * ```\n * const day = new Date(2022, 5, 19);\n * const matcher1: DateRange = {\n *    from: new Date(2021, 12, 21),\n *    to: new Date(2021, 12, 30)\n * }\n * const matcher2: DateRange = {\n *    from: new Date(2022, 5, 1),\n *    to: new Date(2022, 5, 23)\n * }\n *\n * const isMatch(day, [matcher1, matcher2]); // true, since day is in the matcher1 range.\n * ```\n * */\nfunction isMatch(day, matchers) {\n    return matchers.some(function (matcher) {\n        if (typeof matcher === 'boolean') {\n            return matcher;\n        }\n        if (isDateType(matcher)) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(day, matcher);\n        }\n        if (isArrayOfDates(matcher)) {\n            return matcher.includes(day);\n        }\n        if (isDateRange(matcher)) {\n            return isDateInRange(day, matcher);\n        }\n        if (isDayOfWeekType(matcher)) {\n            return matcher.dayOfWeek.includes(day.getDay());\n        }\n        if (isDateInterval(matcher)) {\n            var diffBefore = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(matcher.before, day);\n            var diffAfter = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(matcher.after, day);\n            var isDayBefore = diffBefore > 0;\n            var isDayAfter = diffAfter < 0;\n            var isClosedInterval = (0,date_fns__WEBPACK_IMPORTED_MODULE_19__.isAfter)(matcher.before, matcher.after);\n            if (isClosedInterval) {\n                return isDayAfter && isDayBefore;\n            }\n            else {\n                return isDayBefore || isDayAfter;\n            }\n        }\n        if (isDateAfterType(matcher)) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(day, matcher.after) > 0;\n        }\n        if (isDateBeforeType(matcher)) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(matcher.before, day) > 0;\n        }\n        if (typeof matcher === 'function') {\n            return matcher(day);\n        }\n        return false;\n    });\n}\n\n/** Return the active modifiers for the given day. */\nfunction getActiveModifiers(day, \n/** The modifiers to match for the given date. */\nmodifiers, \n/** The month where the day is displayed, to add the \"outside\" modifiers.  */\ndisplayMonth) {\n    var matchedModifiers = Object.keys(modifiers).reduce(function (result, key) {\n        var modifier = modifiers[key];\n        if (isMatch(day, modifier)) {\n            result.push(key);\n        }\n        return result;\n    }, []);\n    var activeModifiers = {};\n    matchedModifiers.forEach(function (modifier) { return (activeModifiers[modifier] = true); });\n    if (displayMonth && !(0,date_fns__WEBPACK_IMPORTED_MODULE_13__.isSameMonth)(day, displayMonth)) {\n        activeModifiers.outside = true;\n    }\n    return activeModifiers;\n}\n\n/**\n * Returns the day that should be the target of the focus when DayPicker is\n * rendered the first time.\n *\n * TODO: this function doesn't consider if the day is outside the month. We\n * implemented this check in `useDayRender` but it should probably go here. See\n * https://github.com/gpbl/react-day-picker/pull/1576\n */\nfunction getInitialFocusTarget(displayMonths, modifiers) {\n    var firstDayInMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(displayMonths[0]);\n    var lastDayInMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_5__.endOfMonth)(displayMonths[displayMonths.length - 1]);\n    // TODO: cleanup code\n    var firstFocusableDay;\n    var today;\n    var date = firstDayInMonth;\n    while (date <= lastDayInMonth) {\n        var activeModifiers = getActiveModifiers(date, modifiers);\n        var isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n        if (!isFocusable) {\n            date = (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(date, 1);\n            continue;\n        }\n        if (activeModifiers.selected) {\n            return date;\n        }\n        if (activeModifiers.today && !today) {\n            today = date;\n        }\n        if (!firstFocusableDay) {\n            firstFocusableDay = date;\n        }\n        date = (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(date, 1);\n    }\n    if (today) {\n        return today;\n    }\n    else {\n        return firstFocusableDay;\n    }\n}\n\nvar MAX_RETRY = 365;\n/** Return the next date to be focused. */\nfunction getNextFocus(focusedDay, options) {\n    var moveBy = options.moveBy, direction = options.direction, context = options.context, modifiers = options.modifiers, _a = options.retry, retry = _a === void 0 ? { count: 0, lastFocused: focusedDay } : _a;\n    var weekStartsOn = context.weekStartsOn, fromDate = context.fromDate, toDate = context.toDate, locale = context.locale;\n    var moveFns = {\n        day: date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays,\n        week: date_fns__WEBPACK_IMPORTED_MODULE_23__.addWeeks,\n        month: date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths,\n        year: date_fns__WEBPACK_IMPORTED_MODULE_24__.addYears,\n        startOfWeek: function (date) {\n            return context.ISOWeek\n                ? (0,date_fns__WEBPACK_IMPORTED_MODULE_15__.startOfISOWeek)(date)\n                : (0,date_fns__WEBPACK_IMPORTED_MODULE_16__.startOfWeek)(date, { locale: locale, weekStartsOn: weekStartsOn });\n        },\n        endOfWeek: function (date) {\n            return context.ISOWeek\n                ? (0,date_fns__WEBPACK_IMPORTED_MODULE_25__.endOfISOWeek)(date)\n                : (0,date_fns__WEBPACK_IMPORTED_MODULE_26__.endOfWeek)(date, { locale: locale, weekStartsOn: weekStartsOn });\n        }\n    };\n    var newFocusedDay = moveFns[moveBy](focusedDay, direction === 'after' ? 1 : -1);\n    if (direction === 'before' && fromDate) {\n        newFocusedDay = (0,date_fns__WEBPACK_IMPORTED_MODULE_27__.max)([fromDate, newFocusedDay]);\n    }\n    else if (direction === 'after' && toDate) {\n        newFocusedDay = (0,date_fns__WEBPACK_IMPORTED_MODULE_28__.min)([toDate, newFocusedDay]);\n    }\n    var isFocusable = true;\n    if (modifiers) {\n        var activeModifiers = getActiveModifiers(newFocusedDay, modifiers);\n        isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n    }\n    if (isFocusable) {\n        return newFocusedDay;\n    }\n    else {\n        if (retry.count > MAX_RETRY) {\n            return retry.lastFocused;\n        }\n        return getNextFocus(newFocusedDay, {\n            moveBy: moveBy,\n            direction: direction,\n            context: context,\n            modifiers: modifiers,\n            retry: __assign(__assign({}, retry), { count: retry.count + 1 })\n        });\n    }\n}\n\n/**\n * The Focus context shares details about the focused day for the keyboard\n *\n * Access this context from the {@link useFocusContext} hook.\n */\nvar FocusContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** The provider for the {@link FocusContext}. */\nfunction FocusProvider(props) {\n    var navigation = useNavigation();\n    var modifiers = useModifiers();\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(), focusedDay = _a[0], setFocusedDay = _a[1];\n    var _b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(), lastFocused = _b[0], setLastFocused = _b[1];\n    var initialFocusTarget = getInitialFocusTarget(navigation.displayMonths, modifiers);\n    // TODO: cleanup and test obscure code below\n    var focusTarget = (focusedDay !== null && focusedDay !== void 0 ? focusedDay : (lastFocused && navigation.isDateDisplayed(lastFocused)))\n        ? lastFocused\n        : initialFocusTarget;\n    var blur = function () {\n        setLastFocused(focusedDay);\n        setFocusedDay(undefined);\n    };\n    var focus = function (date) {\n        setFocusedDay(date);\n    };\n    var context = useDayPicker();\n    var moveFocus = function (moveBy, direction) {\n        if (!focusedDay)\n            return;\n        var nextFocused = getNextFocus(focusedDay, {\n            moveBy: moveBy,\n            direction: direction,\n            context: context,\n            modifiers: modifiers\n        });\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusedDay, nextFocused))\n            return undefined;\n        navigation.goToDate(nextFocused, focusedDay);\n        focus(nextFocused);\n    };\n    var value = {\n        focusedDay: focusedDay,\n        focusTarget: focusTarget,\n        blur: blur,\n        focus: focus,\n        focusDayAfter: function () { return moveFocus('day', 'after'); },\n        focusDayBefore: function () { return moveFocus('day', 'before'); },\n        focusWeekAfter: function () { return moveFocus('week', 'after'); },\n        focusWeekBefore: function () { return moveFocus('week', 'before'); },\n        focusMonthBefore: function () { return moveFocus('month', 'before'); },\n        focusMonthAfter: function () { return moveFocus('month', 'after'); },\n        focusYearBefore: function () { return moveFocus('year', 'before'); },\n        focusYearAfter: function () { return moveFocus('year', 'after'); },\n        focusStartOfWeek: function () { return moveFocus('startOfWeek', 'before'); },\n        focusEndOfWeek: function () { return moveFocus('endOfWeek', 'after'); }\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FocusContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link FocusContextValue}. Use this hook to handle the\n * focus state of the elements.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useFocusContext() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FocusContext);\n    if (!context) {\n        throw new Error('useFocusContext must be used within a FocusProvider');\n    }\n    return context;\n}\n\n/**\n * Return the active modifiers for the specified day.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n * @param day\n * @param displayMonth\n */\nfunction useActiveModifiers(day, \n/**\n * The month where the date is displayed. If not the same as `date`, the day\n * is an \"outside day\".\n */\ndisplayMonth) {\n    var modifiers = useModifiers();\n    var activeModifiers = getActiveModifiers(day, modifiers, displayMonth);\n    return activeModifiers;\n}\n\n/**\n * The SelectSingle context shares details about the selected days when in\n * single selection mode.\n *\n * Access this context from the {@link useSelectSingle} hook.\n */\nvar SelectSingleContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link SelectSingleProvider}. */\nfunction SelectSingleProvider(props) {\n    if (!isDayPickerSingle(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined\n        };\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectSingleProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b, _c;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        if (activeModifiers.selected && !initialProps.required) {\n            (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, undefined, day, activeModifiers, e);\n            return;\n        }\n        (_c = initialProps.onSelect) === null || _c === void 0 ? void 0 : _c.call(initialProps, day, day, activeModifiers, e);\n    };\n    var contextValue = {\n        selected: initialProps.selected,\n        onDayClick: onDayClick\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleContext.Provider, { value: contextValue, children: children }));\n}\n/**\n * Hook to access the {@link SelectSingleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectSingle() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SelectSingleContext);\n    if (!context) {\n        throw new Error('useSelectSingle must be used within a SelectSingleProvider');\n    }\n    return context;\n}\n\n/**\n * This hook returns details about the content to render in the day cell.\n *\n *\n * When a day cell is rendered in the table, DayPicker can either:\n *\n * - render nothing: when the day is outside the month or has matched the\n *   \"hidden\" modifier.\n * - render a button when `onDayClick` or a selection mode is set.\n * - render a non-interactive element: when no selection mode is set, the day\n *   cell shouldn’t respond to any interaction. DayPicker should render a `div`\n *   or a `span`.\n *\n * ### Usage\n *\n * Use this hook to customize the behavior of the {@link Day} component. Create a\n * new `Day` component using this hook and pass it to the `components` prop.\n * The source of {@link Day} can be a good starting point.\n *\n */\nfunction useDayEventHandlers(date, activeModifiers) {\n    var dayPicker = useDayPicker();\n    var single = useSelectSingle();\n    var multiple = useSelectMultiple();\n    var range = useSelectRange();\n    var _a = useFocusContext(), focusDayAfter = _a.focusDayAfter, focusDayBefore = _a.focusDayBefore, focusWeekAfter = _a.focusWeekAfter, focusWeekBefore = _a.focusWeekBefore, blur = _a.blur, focus = _a.focus, focusMonthBefore = _a.focusMonthBefore, focusMonthAfter = _a.focusMonthAfter, focusYearBefore = _a.focusYearBefore, focusYearAfter = _a.focusYearAfter, focusStartOfWeek = _a.focusStartOfWeek, focusEndOfWeek = _a.focusEndOfWeek;\n    var onClick = function (e) {\n        var _a, _b, _c, _d;\n        if (isDayPickerSingle(dayPicker)) {\n            (_a = single.onDayClick) === null || _a === void 0 ? void 0 : _a.call(single, date, activeModifiers, e);\n        }\n        else if (isDayPickerMultiple(dayPicker)) {\n            (_b = multiple.onDayClick) === null || _b === void 0 ? void 0 : _b.call(multiple, date, activeModifiers, e);\n        }\n        else if (isDayPickerRange(dayPicker)) {\n            (_c = range.onDayClick) === null || _c === void 0 ? void 0 : _c.call(range, date, activeModifiers, e);\n        }\n        else {\n            (_d = dayPicker.onDayClick) === null || _d === void 0 ? void 0 : _d.call(dayPicker, date, activeModifiers, e);\n        }\n    };\n    var onFocus = function (e) {\n        var _a;\n        focus(date);\n        (_a = dayPicker.onDayFocus) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onBlur = function (e) {\n        var _a;\n        blur();\n        (_a = dayPicker.onDayBlur) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onMouseEnter = function (e) {\n        var _a;\n        (_a = dayPicker.onDayMouseEnter) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onMouseLeave = function (e) {\n        var _a;\n        (_a = dayPicker.onDayMouseLeave) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onPointerEnter = function (e) {\n        var _a;\n        (_a = dayPicker.onDayPointerEnter) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onPointerLeave = function (e) {\n        var _a;\n        (_a = dayPicker.onDayPointerLeave) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchCancel = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchCancel) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchEnd = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchEnd) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchMove = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchMove) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchStart = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchStart) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onKeyUp = function (e) {\n        var _a;\n        (_a = dayPicker.onDayKeyUp) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onKeyDown = function (e) {\n        var _a;\n        switch (e.key) {\n            case 'ArrowLeft':\n                e.preventDefault();\n                e.stopPropagation();\n                dayPicker.dir === 'rtl' ? focusDayAfter() : focusDayBefore();\n                break;\n            case 'ArrowRight':\n                e.preventDefault();\n                e.stopPropagation();\n                dayPicker.dir === 'rtl' ? focusDayBefore() : focusDayAfter();\n                break;\n            case 'ArrowDown':\n                e.preventDefault();\n                e.stopPropagation();\n                focusWeekAfter();\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                e.stopPropagation();\n                focusWeekBefore();\n                break;\n            case 'PageUp':\n                e.preventDefault();\n                e.stopPropagation();\n                e.shiftKey ? focusYearBefore() : focusMonthBefore();\n                break;\n            case 'PageDown':\n                e.preventDefault();\n                e.stopPropagation();\n                e.shiftKey ? focusYearAfter() : focusMonthAfter();\n                break;\n            case 'Home':\n                e.preventDefault();\n                e.stopPropagation();\n                focusStartOfWeek();\n                break;\n            case 'End':\n                e.preventDefault();\n                e.stopPropagation();\n                focusEndOfWeek();\n                break;\n        }\n        (_a = dayPicker.onDayKeyDown) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var eventHandlers = {\n        onClick: onClick,\n        onFocus: onFocus,\n        onBlur: onBlur,\n        onKeyDown: onKeyDown,\n        onKeyUp: onKeyUp,\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onPointerEnter: onPointerEnter,\n        onPointerLeave: onPointerLeave,\n        onTouchCancel: onTouchCancel,\n        onTouchEnd: onTouchEnd,\n        onTouchMove: onTouchMove,\n        onTouchStart: onTouchStart\n    };\n    return eventHandlers;\n}\n\n/**\n * Return the current selected days when DayPicker is in selection mode. Days\n * selected by the custom selection mode are not returned.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n */\nfunction useSelectedDays() {\n    var dayPicker = useDayPicker();\n    var single = useSelectSingle();\n    var multiple = useSelectMultiple();\n    var range = useSelectRange();\n    var selectedDays = isDayPickerSingle(dayPicker)\n        ? single.selected\n        : isDayPickerMultiple(dayPicker)\n            ? multiple.selected\n            : isDayPickerRange(dayPicker)\n                ? range.selected\n                : undefined;\n    return selectedDays;\n}\n\nfunction isInternalModifier(modifier) {\n    return Object.values(InternalModifier).includes(modifier);\n}\n/**\n * Return the class names for the Day element, according to the given active\n * modifiers.\n *\n * Custom class names are set via `modifiersClassNames` or `classNames`,\n * where the first have the precedence.\n */\nfunction getDayClassNames(dayPicker, activeModifiers) {\n    var classNames = [dayPicker.classNames.day];\n    Object.keys(activeModifiers).forEach(function (modifier) {\n        var customClassName = dayPicker.modifiersClassNames[modifier];\n        if (customClassName) {\n            classNames.push(customClassName);\n        }\n        else if (isInternalModifier(modifier)) {\n            var internalClassName = dayPicker.classNames[\"day_\".concat(modifier)];\n            if (internalClassName) {\n                classNames.push(internalClassName);\n            }\n        }\n    });\n    return classNames;\n}\n\n/** Return the style for the Day element, according to the given active modifiers. */\nfunction getDayStyle(dayPicker, activeModifiers) {\n    var style = __assign({}, dayPicker.styles.day);\n    Object.keys(activeModifiers).forEach(function (modifier) {\n        var _a;\n        style = __assign(__assign({}, style), (_a = dayPicker.modifiersStyles) === null || _a === void 0 ? void 0 : _a[modifier]);\n    });\n    return style;\n}\n\n/**\n * Return props and data used to render the {@link Day} component.\n *\n * Use this hook when creating a component to replace the built-in `Day`\n * component.\n */\nfunction useDayRender(\n/** The date to render. */\nday, \n/** The month where the date is displayed (if not the same as `date`, it means it is an \"outside\" day). */\ndisplayMonth, \n/** A ref to the button element that will be target of focus when rendered (if required). */\nbuttonRef) {\n    var _a;\n    var _b, _c;\n    var dayPicker = useDayPicker();\n    var focusContext = useFocusContext();\n    var activeModifiers = useActiveModifiers(day, displayMonth);\n    var eventHandlers = useDayEventHandlers(day, activeModifiers);\n    var selectedDays = useSelectedDays();\n    var isButton = Boolean(dayPicker.onDayClick || dayPicker.mode !== 'default');\n    // Focus the button if the day is focused according to the focus context\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        var _a;\n        if (activeModifiers.outside)\n            return;\n        if (!focusContext.focusedDay)\n            return;\n        if (!isButton)\n            return;\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusContext.focusedDay, day)) {\n            (_a = buttonRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n        }\n    }, [\n        focusContext.focusedDay,\n        day,\n        buttonRef,\n        isButton,\n        activeModifiers.outside\n    ]);\n    var className = getDayClassNames(dayPicker, activeModifiers).join(' ');\n    var style = getDayStyle(dayPicker, activeModifiers);\n    var isHidden = Boolean((activeModifiers.outside && !dayPicker.showOutsideDays) ||\n        activeModifiers.hidden);\n    var DayContentComponent = (_c = (_b = dayPicker.components) === null || _b === void 0 ? void 0 : _b.DayContent) !== null && _c !== void 0 ? _c : DayContent;\n    var children = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayContentComponent, { date: day, displayMonth: displayMonth, activeModifiers: activeModifiers }));\n    var divProps = {\n        style: style,\n        className: className,\n        children: children,\n        role: 'gridcell'\n    };\n    var isFocusTarget = focusContext.focusTarget &&\n        (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusContext.focusTarget, day) &&\n        !activeModifiers.outside;\n    var isFocused = focusContext.focusedDay && (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusContext.focusedDay, day);\n    var buttonProps = __assign(__assign(__assign({}, divProps), (_a = { disabled: activeModifiers.disabled, role: 'gridcell' }, _a['aria-selected'] = activeModifiers.selected, _a.tabIndex = isFocused || isFocusTarget ? 0 : -1, _a)), eventHandlers);\n    var dayRender = {\n        isButton: isButton,\n        isHidden: isHidden,\n        activeModifiers: activeModifiers,\n        selectedDays: selectedDays,\n        buttonProps: buttonProps,\n        divProps: divProps\n    };\n    return dayRender;\n}\n\n/**\n * The content of a day cell – as a button or span element according to its\n * modifiers.\n */\nfunction Day(props) {\n    var buttonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var dayRender = useDayRender(props.date, props.displayMonth, buttonRef);\n    if (dayRender.isHidden) {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { role: \"gridcell\" });\n    }\n    if (!dayRender.isButton) {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", __assign({}, dayRender.divProps));\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, __assign({ name: \"day\", ref: buttonRef }, dayRender.buttonProps));\n}\n\n/**\n * Render the week number element. If `onWeekNumberClick` is passed to DayPicker, it\n * renders a button, otherwise a span element.\n */\nfunction WeekNumber(props) {\n    var weekNumber = props.number, dates = props.dates;\n    var _a = useDayPicker(), onWeekNumberClick = _a.onWeekNumberClick, styles = _a.styles, classNames = _a.classNames, locale = _a.locale, labelWeekNumber = _a.labels.labelWeekNumber, formatWeekNumber = _a.formatters.formatWeekNumber;\n    var content = formatWeekNumber(Number(weekNumber), { locale: locale });\n    if (!onWeekNumberClick) {\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: classNames.weeknumber, style: styles.weeknumber, children: content }));\n    }\n    var label = labelWeekNumber(Number(weekNumber), { locale: locale });\n    var handleClick = function (e) {\n        onWeekNumberClick(weekNumber, dates, e);\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, { name: \"week-number\", \"aria-label\": label, className: classNames.weeknumber, style: styles.weeknumber, onClick: handleClick, children: content }));\n}\n\n/** Render a row in the calendar, with the days and the week number. */\nfunction Row(props) {\n    var _a, _b;\n    var _c = useDayPicker(), styles = _c.styles, classNames = _c.classNames, showWeekNumber = _c.showWeekNumber, components = _c.components;\n    var DayComponent = (_a = components === null || components === void 0 ? void 0 : components.Day) !== null && _a !== void 0 ? _a : Day;\n    var WeeknumberComponent = (_b = components === null || components === void 0 ? void 0 : components.WeekNumber) !== null && _b !== void 0 ? _b : WeekNumber;\n    var weekNumberCell;\n    if (showWeekNumber) {\n        weekNumberCell = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: classNames.cell, style: styles.cell, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(WeeknumberComponent, { number: props.weekNumber, dates: props.dates }) }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"tr\", { className: classNames.row, style: styles.row, children: [weekNumberCell, props.dates.map(function (date) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: classNames.cell, style: styles.cell, role: \"presentation\", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayComponent, { displayMonth: props.displayMonth, date: date }) }, (0,date_fns__WEBPACK_IMPORTED_MODULE_29__.getUnixTime)(date))); })] }));\n}\n\n/** Return the weeks between two dates.  */\nfunction daysToMonthWeeks(fromDate, toDate, options) {\n    var toWeek = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_25__.endOfISOWeek)(toDate)\n        : (0,date_fns__WEBPACK_IMPORTED_MODULE_26__.endOfWeek)(toDate, options);\n    var fromWeek = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_15__.startOfISOWeek)(fromDate)\n        : (0,date_fns__WEBPACK_IMPORTED_MODULE_16__.startOfWeek)(fromDate, options);\n    var nOfDays = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(toWeek, fromWeek);\n    var days = [];\n    for (var i = 0; i <= nOfDays; i++) {\n        days.push((0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(fromWeek, i));\n    }\n    var weeksInMonth = days.reduce(function (result, date) {\n        var weekNumber = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n            ? (0,date_fns__WEBPACK_IMPORTED_MODULE_30__.getISOWeek)(date)\n            : (0,date_fns__WEBPACK_IMPORTED_MODULE_31__.getWeek)(date, options);\n        var existingWeek = result.find(function (value) { return value.weekNumber === weekNumber; });\n        if (existingWeek) {\n            existingWeek.dates.push(date);\n            return result;\n        }\n        result.push({\n            weekNumber: weekNumber,\n            dates: [date]\n        });\n        return result;\n    }, []);\n    return weeksInMonth;\n}\n\n/**\n * Return the weeks belonging to the given month, adding the \"outside days\" to\n * the first and last week.\n */\nfunction getMonthWeeks(month, options) {\n    var weeksInMonth = daysToMonthWeeks((0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(month), (0,date_fns__WEBPACK_IMPORTED_MODULE_5__.endOfMonth)(month), options);\n    if (options === null || options === void 0 ? void 0 : options.useFixedWeeks) {\n        // Add extra weeks to the month, up to 6 weeks\n        var nrOfMonthWeeks = (0,date_fns__WEBPACK_IMPORTED_MODULE_32__.getWeeksInMonth)(month, options);\n        if (nrOfMonthWeeks < 6) {\n            var lastWeek = weeksInMonth[weeksInMonth.length - 1];\n            var lastDate = lastWeek.dates[lastWeek.dates.length - 1];\n            var toDate = (0,date_fns__WEBPACK_IMPORTED_MODULE_23__.addWeeks)(lastDate, 6 - nrOfMonthWeeks);\n            var extraWeeks = daysToMonthWeeks((0,date_fns__WEBPACK_IMPORTED_MODULE_23__.addWeeks)(lastDate, 1), toDate, options);\n            weeksInMonth.push.apply(weeksInMonth, extraWeeks);\n        }\n    }\n    return weeksInMonth;\n}\n\n/** Render the table with the calendar. */\nfunction Table(props) {\n    var _a, _b, _c;\n    var _d = useDayPicker(), locale = _d.locale, classNames = _d.classNames, styles = _d.styles, hideHead = _d.hideHead, fixedWeeks = _d.fixedWeeks, components = _d.components, weekStartsOn = _d.weekStartsOn, firstWeekContainsDate = _d.firstWeekContainsDate, ISOWeek = _d.ISOWeek;\n    var weeks = getMonthWeeks(props.displayMonth, {\n        useFixedWeeks: Boolean(fixedWeeks),\n        ISOWeek: ISOWeek,\n        locale: locale,\n        weekStartsOn: weekStartsOn,\n        firstWeekContainsDate: firstWeekContainsDate\n    });\n    var HeadComponent = (_a = components === null || components === void 0 ? void 0 : components.Head) !== null && _a !== void 0 ? _a : Head;\n    var RowComponent = (_b = components === null || components === void 0 ? void 0 : components.Row) !== null && _b !== void 0 ? _b : Row;\n    var FooterComponent = (_c = components === null || components === void 0 ? void 0 : components.Footer) !== null && _c !== void 0 ? _c : Footer;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"table\", { id: props.id, className: classNames.table, style: styles.table, role: \"grid\", \"aria-labelledby\": props['aria-labelledby'], children: [!hideHead && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(HeadComponent, {}), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tbody\", { className: classNames.tbody, style: styles.tbody, children: weeks.map(function (week) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(RowComponent, { displayMonth: props.displayMonth, dates: week.dates, weekNumber: week.weekNumber }, week.weekNumber)); }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FooterComponent, { displayMonth: props.displayMonth })] }));\n}\n\n/*\nThe MIT License (MIT)\n\nCopyright (c) 2018-present, React Training LLC\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n/* eslint-disable prefer-const */\n/* eslint-disable @typescript-eslint/ban-ts-comment */\n/*\n * Welcome to @reach/auto-id!\n * Let's see if we can make sense of why this hook exists and its\n * implementation.\n *\n * Some background:\n *   1. Accessibility APIs rely heavily on element IDs\n *   2. Requiring developers to put IDs on every element in Reach UI is both\n *      cumbersome and error-prone\n *   3. With a component model, we can generate IDs for them!\n *\n * Solution 1: Generate random IDs.\n *\n * This works great as long as you don't server render your app. When React (in\n * the client) tries to reuse the markup from the server, the IDs won't match\n * and React will then recreate the entire DOM tree.\n *\n * Solution 2: Increment an integer\n *\n * This sounds great. Since we're rendering the exact same tree on the server\n * and client, we can increment a counter and get a deterministic result between\n * client and server. Also, JS integers can go up to nine-quadrillion. I'm\n * pretty sure the tab will be closed before an app never needs\n * 10 quadrillion IDs!\n *\n * Problem solved, right?\n *\n * Ah, but there's a catch! React's concurrent rendering makes this approach\n * non-deterministic. While the client and server will end up with the same\n * elements in the end, depending on suspense boundaries (and possibly some user\n * input during the initial render) the incrementing integers won't always match\n * up.\n *\n * Solution 3: Don't use IDs at all on the server; patch after first render.\n *\n * What we've done here is solution 2 with some tricks. With this approach, the\n * ID returned is an empty string on the first render. This way the server and\n * client have the same markup no matter how wild the concurrent rendering may\n * have gotten.\n *\n * After the render, we patch up the components with an incremented ID. This\n * causes a double render on any components with `useId`. Shouldn't be a problem\n * since the components using this hook should be small, and we're only updating\n * the ID attribute on the DOM, nothing big is happening.\n *\n * It doesn't have to be an incremented number, though--we could do generate\n * random strings instead, but incrementing a number is probably the cheapest\n * thing we can do.\n *\n * Additionally, we only do this patchup on the very first client render ever.\n * Any calls to `useId` that happen dynamically in the client will be\n * populated immediately with a value. So, we only get the double render after\n * server hydration and never again, SO BACK OFF ALRIGHT?\n */\nfunction canUseDOM() {\n    return !!(typeof window !== 'undefined' &&\n        window.document &&\n        window.document.createElement);\n}\n/**\n * React currently throws a warning when using useLayoutEffect on the server. To\n * get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect in the browser. We occasionally need useLayoutEffect to\n * ensure we don't get a render flash for certain operations, but we may also\n * need affected components to render on the server. One example is when setting\n * a component's descendants to retrieve their index values.\n *\n * Important to note that using this hook as an escape hatch will break the\n * eslint dependency warnings unless you rename the import to `useLayoutEffect`.\n * Use sparingly only when the effect won't effect the rendered HTML to avoid\n * any server/client mismatch.\n *\n * If a useLayoutEffect is needed and the result would create a mismatch, it's\n * likely that the component in question shouldn't be rendered on the server at\n * all, so a better approach would be to lazily render those in a parent\n * component after client-side hydration.\n *\n * https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n * https://github.com/reduxjs/react-redux/blob/master/src/utils/useIsomorphicLayoutEffect.js\n *\n * @param effect\n * @param deps\n */\nvar useIsomorphicLayoutEffect = canUseDOM() ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\nvar serverHandoffComplete = false;\nvar id = 0;\nfunction genId() {\n    return \"react-day-picker-\".concat(++id);\n}\nfunction useId(providedId) {\n    // TODO: Remove error flag when updating internal deps to React 18. None of\n    // our tricks will play well with concurrent rendering anyway.\n    var _a;\n    // If this instance isn't part of the initial render, we don't have to do the\n    // double render/patch-up dance. We can just generate the ID and return it.\n    var initialId = providedId !== null && providedId !== void 0 ? providedId : (serverHandoffComplete ? genId() : null);\n    var _b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialId), id = _b[0], setId = _b[1];\n    useIsomorphicLayoutEffect(function () {\n        if (id === null) {\n            // Patch the ID after render. We do this in `useLayoutEffect` to avoid any\n            // rendering flicker, though it'll make the first render slower (unlikely\n            // to matter, but you're welcome to measure your app and let us know if\n            // it's a problem).\n            setId(genId());\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        if (serverHandoffComplete === false) {\n            // Flag all future uses of `useId` to skip the update dance. This is in\n            // `useEffect` because it goes after `useLayoutEffect`, ensuring we don't\n            // accidentally bail out of the patch-up dance prematurely.\n            serverHandoffComplete = true;\n        }\n    }, []);\n    return (_a = providedId !== null && providedId !== void 0 ? providedId : id) !== null && _a !== void 0 ? _a : undefined;\n}\n\n/** Render a month. */\nfunction Month(props) {\n    var _a;\n    var _b;\n    var dayPicker = useDayPicker();\n    var dir = dayPicker.dir, classNames = dayPicker.classNames, styles = dayPicker.styles, components = dayPicker.components;\n    var displayMonths = useNavigation().displayMonths;\n    var captionId = useId(dayPicker.id ? \"\".concat(dayPicker.id, \"-\").concat(props.displayIndex) : undefined);\n    var tableId = dayPicker.id\n        ? \"\".concat(dayPicker.id, \"-grid-\").concat(props.displayIndex)\n        : undefined;\n    var className = [classNames.month];\n    var style = styles.month;\n    var isStart = props.displayIndex === 0;\n    var isEnd = props.displayIndex === displayMonths.length - 1;\n    var isCenter = !isStart && !isEnd;\n    if (dir === 'rtl') {\n        _a = [isStart, isEnd], isEnd = _a[0], isStart = _a[1];\n    }\n    if (isStart) {\n        className.push(classNames.caption_start);\n        style = __assign(__assign({}, style), styles.caption_start);\n    }\n    if (isEnd) {\n        className.push(classNames.caption_end);\n        style = __assign(__assign({}, style), styles.caption_end);\n    }\n    if (isCenter) {\n        className.push(classNames.caption_between);\n        style = __assign(__assign({}, style), styles.caption_between);\n    }\n    var CaptionComponent = (_b = components === null || components === void 0 ? void 0 : components.Caption) !== null && _b !== void 0 ? _b : Caption;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: className.join(' '), style: style, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionComponent, { id: captionId, displayMonth: props.displayMonth, displayIndex: props.displayIndex }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Table, { id: tableId, \"aria-labelledby\": captionId, displayMonth: props.displayMonth })] }, props.displayIndex));\n}\n\n/**\n * Render the wrapper for the month grids.\n */\nfunction Months(props) {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.months, style: styles.months, children: props.children }));\n}\n\n/** Render the container with the months according to the number of months to display. */\nfunction Root(_a) {\n    var _b, _c;\n    var initialProps = _a.initialProps;\n    var dayPicker = useDayPicker();\n    var focusContext = useFocusContext();\n    var navigation = useNavigation();\n    var _d = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), hasInitialFocus = _d[0], setHasInitialFocus = _d[1];\n    // Focus the focus target when initialFocus is passed in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        if (!dayPicker.initialFocus)\n            return;\n        if (!focusContext.focusTarget)\n            return;\n        if (hasInitialFocus)\n            return;\n        focusContext.focus(focusContext.focusTarget);\n        setHasInitialFocus(true);\n    }, [\n        dayPicker.initialFocus,\n        hasInitialFocus,\n        focusContext.focus,\n        focusContext.focusTarget,\n        focusContext\n    ]);\n    // Apply classnames according to props\n    var classNames = [dayPicker.classNames.root, dayPicker.className];\n    if (dayPicker.numberOfMonths > 1) {\n        classNames.push(dayPicker.classNames.multiple_months);\n    }\n    if (dayPicker.showWeekNumber) {\n        classNames.push(dayPicker.classNames.with_weeknumber);\n    }\n    var style = __assign(__assign({}, dayPicker.styles.root), dayPicker.style);\n    var dataAttributes = Object.keys(initialProps)\n        .filter(function (key) { return key.startsWith('data-'); })\n        .reduce(function (attrs, key) {\n        var _a;\n        return __assign(__assign({}, attrs), (_a = {}, _a[key] = initialProps[key], _a));\n    }, {});\n    var MonthsComponent = (_c = (_b = initialProps.components) === null || _b === void 0 ? void 0 : _b.Months) !== null && _c !== void 0 ? _c : Months;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", __assign({ className: classNames.join(' '), style: style, dir: dayPicker.dir, id: dayPicker.id, nonce: initialProps.nonce, title: initialProps.title, lang: initialProps.lang }, dataAttributes, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MonthsComponent, { children: navigation.displayMonths.map(function (month, i) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Month, { displayIndex: i, displayMonth: month }, i)); }) }) })));\n}\n\n/** Provide the value for all the context providers. */\nfunction RootProvider(props) {\n    var children = props.children, initialProps = __rest(props, [\"children\"]);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayPickerProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(NavigationProvider, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ModifiersProvider, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FocusProvider, { children: children }) }) }) }) }) }) }));\n}\n\n/**\n * DayPicker render a date picker component to let users pick dates from a\n * calendar. See http://react-day-picker.js.org for updated documentation and\n * examples.\n *\n * ### Customization\n *\n * DayPicker offers different customization props. For example,\n *\n * - show multiple months using `numberOfMonths`\n * - display a dropdown to navigate the months via `captionLayout`\n * - display the week numbers with `showWeekNumbers`\n * - disable or hide days with `disabled` or `hidden`\n *\n * ### Controlling the months\n *\n * Change the initially displayed month using the `defaultMonth` prop. The\n * displayed months are controlled by DayPicker and stored in its internal\n * state. To control the months yourself, use `month` instead of `defaultMonth`\n * and use the `onMonthChange` event to set it.\n *\n * To limit the months the user can navigate to, use\n * `fromDate`/`fromMonth`/`fromYear` or `toDate`/`toMonth`/`toYear`.\n *\n * ### Selection modes\n *\n * DayPicker supports different selection mode that can be toggled using the\n * `mode` prop:\n *\n * - `mode=\"single\"`: only one day can be selected. Use `required` to make the\n *   selection required. Use the `onSelect` event handler to get the selected\n *   days.\n * - `mode=\"multiple\"`: users can select one or more days. Limit the amount of\n *   days that can be selected with the `min` or the `max` props.\n * - `mode=\"range\"`: users can select a range of days. Limit the amount of days\n *   in the range with the `min` or the `max` props.\n * - `mode=\"default\"` (default): the built-in selections are disabled. Implement\n *   your own selection mode with `onDayClick`.\n *\n * The selection modes should cover the most common use cases. In case you\n * need a more refined way of selecting days, use `mode=\"default\"`. Use the\n * `selected` props and add the day event handlers to add/remove days from the\n * selection.\n *\n * ### Modifiers\n *\n * A _modifier_ represents different styles or states for the days displayed in\n * the calendar (like \"selected\" or \"disabled\"). Define custom modifiers using\n * the `modifiers` prop.\n *\n * ### Formatters and custom component\n *\n * You can customize how the content is displayed in the date picker by using\n * either the formatters or replacing the internal components.\n *\n * For the most common cases you want to use the `formatters` prop to change how\n * the content is formatted in the calendar. Use the `components` prop to\n * replace the internal components, like the navigation icons.\n *\n * ### Styling\n *\n * DayPicker comes with a default, basic style in `react-day-picker/style` – use\n * it as template for your own style.\n *\n * If you are using CSS modules, pass the imported styles object the\n * `classNames` props.\n *\n * You can also style the elements via inline styles using the `styles` prop.\n *\n * ### Form fields\n *\n * If you need to bind the date picker to a form field, you can use the\n * `useInput` hooks for a basic behavior. See the `useInput` source as an\n * example to bind the date picker with form fields.\n *\n * ### Localization\n *\n * To localize DayPicker, import the locale from `date-fns` package and use the\n * `locale` prop.\n *\n * For example, to use Spanish locale:\n *\n * ```\n * import { es } from 'date-fns/locale';\n * <DayPicker locale={es} />\n * ```\n */\nfunction DayPicker(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(RootProvider, __assign({}, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Root, { initialProps: props }) })));\n}\n\n/** @private */\nfunction isValidDate(day) {\n    return !isNaN(day.getTime());\n}\n\n/** Return props and setters for binding an input field to DayPicker. */\nfunction useInput(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.locale, locale = _a === void 0 ? date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.enUS : _a, required = options.required, _b = options.format, format$1 = _b === void 0 ? 'PP' : _b, defaultSelected = options.defaultSelected, _c = options.today, today = _c === void 0 ? new Date() : _c;\n    var _d = parseFromToProps(options), fromDate = _d.fromDate, toDate = _d.toDate;\n    // Shortcut to the DateFns functions\n    var parseValue = function (value) { return (0,date_fns__WEBPACK_IMPORTED_MODULE_33__.parse)(value, format$1, today, { locale: locale }); };\n    // Initialize states\n    var _e = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSelected !== null && defaultSelected !== void 0 ? defaultSelected : today), month = _e[0], setMonth = _e[1];\n    var _f = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSelected), selectedDay = _f[0], setSelectedDay = _f[1];\n    var defaultInputValue = defaultSelected\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(defaultSelected, format$1, { locale: locale })\n        : '';\n    var _g = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultInputValue), inputValue = _g[0], setInputValue = _g[1];\n    var reset = function () {\n        setSelectedDay(defaultSelected);\n        setMonth(defaultSelected !== null && defaultSelected !== void 0 ? defaultSelected : today);\n        setInputValue(defaultInputValue !== null && defaultInputValue !== void 0 ? defaultInputValue : '');\n    };\n    var setSelected = function (date) {\n        setSelectedDay(date);\n        setMonth(date !== null && date !== void 0 ? date : today);\n        setInputValue(date ? (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(date, format$1, { locale: locale }) : '');\n    };\n    var handleDayClick = function (day, _a) {\n        var selected = _a.selected;\n        if (!required && selected) {\n            setSelectedDay(undefined);\n            setInputValue('');\n            return;\n        }\n        setSelectedDay(day);\n        setInputValue(day ? (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, format$1, { locale: locale }) : '');\n    };\n    var handleMonthChange = function (month) {\n        setMonth(month);\n    };\n    // When changing the input field, save its value in state and check if the\n    // string is a valid date. If it is a valid day, set it as selected and update\n    // the calendar’s month.\n    var handleChange = function (e) {\n        setInputValue(e.target.value);\n        var day = parseValue(e.target.value);\n        var isBefore = fromDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(fromDate, day) > 0;\n        var isAfter = toDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(day, toDate) > 0;\n        if (!isValidDate(day) || isBefore || isAfter) {\n            setSelectedDay(undefined);\n            return;\n        }\n        setSelectedDay(day);\n        setMonth(day);\n    };\n    // Special case for _required_ fields: on blur, if the value of the input is not\n    // a valid date, reset the calendar and the input value.\n    var handleBlur = function (e) {\n        var day = parseValue(e.target.value);\n        if (!isValidDate(day)) {\n            reset();\n        }\n    };\n    // When focusing, make sure DayPicker visualizes the month of the date in the\n    // input field.\n    var handleFocus = function (e) {\n        if (!e.target.value) {\n            reset();\n            return;\n        }\n        var day = parseValue(e.target.value);\n        if (isValidDate(day)) {\n            setMonth(day);\n        }\n    };\n    var dayPickerProps = {\n        month: month,\n        onDayClick: handleDayClick,\n        onMonthChange: handleMonthChange,\n        selected: selectedDay,\n        locale: locale,\n        fromDate: fromDate,\n        toDate: toDate,\n        today: today\n    };\n    var inputProps = {\n        onBlur: handleBlur,\n        onChange: handleChange,\n        onFocus: handleFocus,\n        value: inputValue,\n        placeholder: (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(new Date(), format$1, { locale: locale })\n    };\n    return { dayPickerProps: dayPickerProps, inputProps: inputProps, reset: reset, setSelected: setSelected };\n}\n\n/** Returns true when the props are of type {@link DayPickerDefaultProps}. */\nfunction isDayPickerDefault(props) {\n    return props.mode === undefined || props.mode === 'default';\n}\n\n\n//# sourceMappingURL=index.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-day-picker/dist/index.esm.js\n");

/***/ })

};
;