"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_stagewise_toolbar-next_dist_index_js"],{

/***/ "(app-pages-browser)/./node_modules/@stagewise/toolbar-next/dist/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@stagewise/toolbar-next/dist/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StagewiseToolbar: () => (/* binding */ StagewiseToolbar)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ StagewiseToolbar auto */ \n\nvar jsxRuntime = {\n    exports: {}\n};\nvar reactJsxRuntime_production = {};\n/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ var hasRequiredReactJsxRuntime_production;\nfunction requireReactJsxRuntime_production() {\n    if (hasRequiredReactJsxRuntime_production) return reactJsxRuntime_production;\n    hasRequiredReactJsxRuntime_production = 1;\n    var REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"), REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\n    function jsxProd(type, config, maybeKey) {\n        var key = null;\n        void 0 !== maybeKey && (key = \"\" + maybeKey);\n        void 0 !== config.key && (key = \"\" + config.key);\n        if (\"key\" in config) {\n            maybeKey = {};\n            for(var propName in config)\"key\" !== propName && (maybeKey[propName] = config[propName]);\n        } else maybeKey = config;\n        config = maybeKey.ref;\n        return {\n            $$typeof: REACT_ELEMENT_TYPE,\n            type,\n            key,\n            ref: void 0 !== config ? config : null,\n            props: maybeKey\n        };\n    }\n    reactJsxRuntime_production.Fragment = REACT_FRAGMENT_TYPE;\n    reactJsxRuntime_production.jsx = jsxProd;\n    reactJsxRuntime_production.jsxs = jsxProd;\n    return reactJsxRuntime_production;\n}\nvar reactJsxRuntime_development = {};\n/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ var hasRequiredReactJsxRuntime_development;\nfunction requireReactJsxRuntime_development() {\n    if (hasRequiredReactJsxRuntime_development) return reactJsxRuntime_development;\n    hasRequiredReactJsxRuntime_development = 1;\n     true && function() {\n        function getComponentNameFromType(type) {\n            if (null == type) return null;\n            if (\"function\" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;\n            if (\"string\" === typeof type) return type;\n            switch(type){\n                case REACT_FRAGMENT_TYPE:\n                    return \"Fragment\";\n                case REACT_PROFILER_TYPE:\n                    return \"Profiler\";\n                case REACT_STRICT_MODE_TYPE:\n                    return \"StrictMode\";\n                case REACT_SUSPENSE_TYPE:\n                    return \"Suspense\";\n                case REACT_SUSPENSE_LIST_TYPE:\n                    return \"SuspenseList\";\n                case REACT_ACTIVITY_TYPE:\n                    return \"Activity\";\n            }\n            if (\"object\" === typeof type) switch(\"number\" === typeof type.tag && console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"), type.$$typeof){\n                case REACT_PORTAL_TYPE:\n                    return \"Portal\";\n                case REACT_CONTEXT_TYPE:\n                    return (type.displayName || \"Context\") + \".Provider\";\n                case REACT_CONSUMER_TYPE:\n                    return (type._context.displayName || \"Context\") + \".Consumer\";\n                case REACT_FORWARD_REF_TYPE:\n                    var innerType = type.render;\n                    type = type.displayName;\n                    type || (type = innerType.displayName || innerType.name || \"\", type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\");\n                    return type;\n                case REACT_MEMO_TYPE:\n                    return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || \"Memo\";\n                case REACT_LAZY_TYPE:\n                    innerType = type._payload;\n                    type = type._init;\n                    try {\n                        return getComponentNameFromType(type(innerType));\n                    } catch (x) {}\n            }\n            return null;\n        }\n        function testStringCoercion(value) {\n            return \"\" + value;\n        }\n        function checkKeyStringCoercion(value) {\n            try {\n                testStringCoercion(value);\n                var JSCompiler_inline_result = false;\n            } catch (e) {\n                JSCompiler_inline_result = true;\n            }\n            if (JSCompiler_inline_result) {\n                JSCompiler_inline_result = console;\n                var JSCompiler_temp_const = JSCompiler_inline_result.error;\n                var JSCompiler_inline_result$jscomp$0 = \"function\" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || \"Object\";\n                JSCompiler_temp_const.call(JSCompiler_inline_result, \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\", JSCompiler_inline_result$jscomp$0);\n                return testStringCoercion(value);\n            }\n        }\n        function getTaskName(type) {\n            if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n            if (\"object\" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return \"<...>\";\n            try {\n                var name = getComponentNameFromType(type);\n                return name ? \"<\" + name + \">\" : \"<...>\";\n            } catch (x) {\n                return \"<...>\";\n            }\n        }\n        function getOwner() {\n            var dispatcher = ReactSharedInternals.A;\n            return null === dispatcher ? null : dispatcher.getOwner();\n        }\n        function UnknownOwner() {\n            return Error(\"react-stack-top-frame\");\n        }\n        function hasValidKey(config) {\n            if (hasOwnProperty.call(config, \"key\")) {\n                var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n                if (getter && getter.isReactWarning) return false;\n            }\n            return void 0 !== config.key;\n        }\n        function defineKeyPropWarningGetter(props, displayName) {\n            function warnAboutAccessingKey() {\n                specialPropKeyWarningShown || (specialPropKeyWarningShown = true, console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\", displayName));\n            }\n            warnAboutAccessingKey.isReactWarning = true;\n            Object.defineProperty(props, \"key\", {\n                get: warnAboutAccessingKey,\n                configurable: true\n            });\n        }\n        function elementRefGetterWithDeprecationWarning() {\n            var componentName = getComponentNameFromType(this.type);\n            didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = true, console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"));\n            componentName = this.props.ref;\n            return void 0 !== componentName ? componentName : null;\n        }\n        function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {\n            self = props.ref;\n            type = {\n                $$typeof: REACT_ELEMENT_TYPE,\n                type,\n                key,\n                props,\n                _owner: owner\n            };\n            null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, \"ref\", {\n                enumerable: false,\n                get: elementRefGetterWithDeprecationWarning\n            }) : Object.defineProperty(type, \"ref\", {\n                enumerable: false,\n                value: null\n            });\n            type._store = {};\n            Object.defineProperty(type._store, \"validated\", {\n                configurable: false,\n                enumerable: false,\n                writable: true,\n                value: 0\n            });\n            Object.defineProperty(type, \"_debugInfo\", {\n                configurable: false,\n                enumerable: false,\n                writable: true,\n                value: null\n            });\n            Object.defineProperty(type, \"_debugStack\", {\n                configurable: false,\n                enumerable: false,\n                writable: true,\n                value: debugStack\n            });\n            Object.defineProperty(type, \"_debugTask\", {\n                configurable: false,\n                enumerable: false,\n                writable: true,\n                value: debugTask\n            });\n            Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n            return type;\n        }\n        function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {\n            var children = config.children;\n            if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {\n                for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);\n                Object.freeze && Object.freeze(children);\n            } else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");\n            else validateChildKeys(children);\n            if (hasOwnProperty.call(config, \"key\")) {\n                children = getComponentNameFromType(type);\n                var keys = Object.keys(config).filter(function(k) {\n                    return \"key\" !== k;\n                });\n                isStaticChildren = 0 < keys.length ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\" : \"{key: someKey}\";\n                didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\", console.error('A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = true);\n            }\n            children = null;\n            void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = \"\" + maybeKey);\n            hasValidKey(config) && (checkKeyStringCoercion(config.key), children = \"\" + config.key);\n            if (\"key\" in config) {\n                maybeKey = {};\n                for(var propName in config)\"key\" !== propName && (maybeKey[propName] = config[propName]);\n            } else maybeKey = config;\n            children && defineKeyPropWarningGetter(maybeKey, \"function\" === typeof type ? type.displayName || type.name || \"Unknown\" : type);\n            return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);\n        }\n        function validateChildKeys(node) {\n            \"object\" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);\n        }\n        var React = react__WEBPACK_IMPORTED_MODULE_0__, REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"), REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"), REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"), REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"), REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n        var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"), REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"), REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"), REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"), REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"), REACT_MEMO_TYPE = Symbol.for(\"react.memo\"), REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"), REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"), REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {\n            return null;\n        };\n        React = {\n            \"react-stack-bottom-frame\": function(callStackForError) {\n                return callStackForError();\n            }\n        };\n        var specialPropKeyWarningShown;\n        var didWarnAboutElementRef = {};\n        var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(React, UnknownOwner)();\n        var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n        var didWarnAboutKeySpread = {};\n        reactJsxRuntime_development.Fragment = REACT_FRAGMENT_TYPE;\n        reactJsxRuntime_development.jsx = function(type, config, maybeKey, source, self) {\n            var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n            return jsxDEVImpl(type, config, maybeKey, false, source, self, trackActualOwner ? Error(\"react-stack-top-frame\") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);\n        };\n        reactJsxRuntime_development.jsxs = function(type, config, maybeKey, source, self) {\n            var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n            return jsxDEVImpl(type, config, maybeKey, true, source, self, trackActualOwner ? Error(\"react-stack-top-frame\") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);\n        };\n    }();\n    return reactJsxRuntime_development;\n}\nvar hasRequiredJsxRuntime;\nfunction requireJsxRuntime() {\n    if (hasRequiredJsxRuntime) return jsxRuntime.exports;\n    hasRequiredJsxRuntime = 1;\n    if (false) {} else {\n        jsxRuntime.exports = requireReactJsxRuntime_development();\n    }\n    return jsxRuntime.exports;\n}\nvar jsxRuntimeExports = requireJsxRuntime();\nconst DynamicToolbar = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_stagewise_toolbar-react_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @stagewise/toolbar-react */ \"(app-pages-browser)/./node_modules/@stagewise/toolbar-react/dist/index.js\")).then((mod)=>({\n            default: mod.StagewiseToolbar\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"node_modules\\\\@stagewise\\\\toolbar-next\\\\dist\\\\index.js -> \" + \"@stagewise/toolbar-react\"\n        ]\n    },\n    ssr: false\n});\n_c = DynamicToolbar;\nconst StagewiseToolbar = (param)=>{\n    let { config, enabled = \"development\" === \"development\" } = param;\n    if (!enabled) {\n        return null;\n    }\n    return /* @__PURE__ */ jsxRuntimeExports.jsx(DynamicToolbar, {\n        config,\n        enabled\n    });\n};\n_c1 = StagewiseToolbar;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"DynamicToolbar\");\n$RefreshReg$(_c1, \"StagewiseToolbar\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@stagewise/toolbar-next/dist/index.js\n"));

/***/ })

}]);